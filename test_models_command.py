#!/usr/bin/env python3
from helpers import gemini_cli, groq_cli, get_gemini_models, get_groq_models

print("Testing model fetching functions...\n")

# Test Gemini models
print("GEMINI MODELS:")
if gemini_cli is not None:
    try:
        models = get_gemini_models(gemini_cli)
        print(f"Successfully fetched {len(models)} Gemini models:")
        for model in models[:10]:  # Show first 10 models
            print(f"- {model}")
        if len(models) > 10:
            print(f"... and {len(models) - 10} more")
    except Exception as e:
        print(f"Error fetching Gemini models: {e}")
else:
    print("Google API client is not initialized")

print("\nGROQ MODELS:")
if groq_cli is not None:
    try:
        models = get_groq_models(groq_cli)
        print(f"Successfully fetched {len(models)} Groq models:")
        for model in models:
            print(f"- {model}")
    except Exception as e:
        print(f"Error fetching Groq models: {e}")
else:
    print("Groq API client is not initialized")
