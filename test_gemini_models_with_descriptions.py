#!/usr/bin/env python3
from helpers import gemini_cli, get_gemini_models

print("Testing Gemini models with descriptions...\n")

if gemini_cli is not None:
    try:
        # Get models with descriptions
        models = get_gemini_models(gemini_cli, include_descriptions=True)
        
        print(f"Successfully fetched {len(models)} Gemini models with descriptions:\n")
        
        # Display first 5 models with their descriptions
        for i, model in enumerate(models[:5]):
            print(f"{i+1}. {model['name']}")
            if model['display_name']:
                print(f"   Display name: {model['display_name']}")
            if model['description']:
                print(f"   Description: {model['description']}")
            print()
            
        if len(models) > 5:
            print(f"... and {len(models) - 5} more models")
    except Exception as e:
        print(f"Error fetching Gemini models: {e}")
else:
    print("Google API client is not initialized")
