from pyrogram import Client, filters
from helpers import db, gemini_cli
from google.genai import types
import asyncio

# Store active chats
active_chats = {}

@Client.on_message(filters.command(['chat', 'c'], prefixes=['.', '!']) & filters.me)
async def chat_handler(client, message):
    try:
        chat_id = message.chat.id
        cmd = message.command

        # Show help if no input
        if len(cmd) < 2:
            await message.edit(
                "**Gemini Chat Commands:**\n"
                "- `.chat <message>`: Send message to Gemini\n"
                "- `.chat clear`: Clear chat history\n"
                "- `.chat history`: Show chat history\n\n"
                "Example: `.chat tell me about quantum computing`"
            )
            return

        action = cmd[1].lower()

        # Handle clear command
        if action == 'clear':
            if chat_id in active_chats:
                del active_chats[chat_id]
            await message.edit("🧹 Chat history cleared!")
            return

        # Handle history command
        if action == 'history':
            if chat_id not in active_chats:
                await message.edit("No chat history found!")
                return
            
            history = ""
            for msg in active_chats[chat_id].get_history():
                history += f"**{msg.role}**: {msg.parts[0].text}\n\n"
            
            await message.edit(f"**Chat History:**\n\n{history}")
            return

        # Initialize chat if needed
        if chat_id not in active_chats:
            active_chats[chat_id] = gemini_cli.chats.create(
                model="gemini-2.0-flash",
                config=types.GenerateContentConfig(
                    temperature=0.7,
                    max_output_tokens=2048
                )
            )

        # Get user message
        user_message = ' '.join(cmd[1:])
        
        # Show typing indicator
        await message.edit("🤔 Thinking...")

        # Send message and get response
        response = active_chats[chat_id].send_message(user_message)
        
        # Edit original message with response
        await message.edit(response.text)

    except Exception as e:
        error_msg = str(e)
        if len(error_msg) > 100:
            error_msg = error_msg[:100] + "..."
        await message.edit(f"⚠️ Error: {error_msg}")