from google.genai import types
from pyrogram import Client, filters
from helpers import cloudflare_ai, groq_cli, gemini_cli, db # gemini_cli is the configured genai client
import base64
import os
import wave



# --- TTS Voices ---
# Supported languages for Cloudflare
SUPPORTED_LANGUAGES = ['en', 'fr', 'es', 'de', 'it', 'pt', 'pl', 'tr', 'ru', 'nl', 'cs', 'ar', 'zh', 'ja', 'ko']

# Groq TTS voices
GROQ_ENGLISH_VOICES = [
    'Arista-PlayAI', 'Atlas-PlayAI', 'Basil-PlayAI', 'Briggs-PlayAI',
    '<PERSON>um-PlayAI', 'Celeste-PlayAI', 'Cheyenne-PlayAI', 'Chip-PlayAI',
    '<PERSON><PERSON><PERSON>-<PERSON>A<PERSON>', '<PERSON><PERSON><PERSON>-<PERSON>A<PERSON>', 'Fritz-PlayAI', '<PERSON>-PlayAI',
    'Indigo-PlayAI', '<PERSON>w-PlayAI', '<PERSON>-<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>-<PERSON>A<PERSON>', '<PERSON>-<PERSON>A<PERSON>'
]
GROQ_ARABIC_VOICES = ['<PERSON>-PlayAI', '<PERSON><PERSON>-<PERSON>AI', 'Khalid-PlayAI', 'Nasser-PlayAI']

# Gemini TTS voices (official voices from documentation)
GE<PERSON>NI_VOICES = [
    "Zephyr", "Puck", "Charon", "<PERSON>re", "<PERSON>rir", "Leda", "Orus", "Aoede",
    "Callirrhoe", "<PERSON>noe", "Enceladus", "Iapetus", "Umbriel", "Algieba",
    "Despina", "Erinome", "Algenib", "Rasalgethi", "Laomedeia", "Achernar",
    "Alnilam", "Schedar", "Gacrux", "Pulcherrima", "Achird", "Zubenelgenubi",
    "Vindemiatrix", "Sadachbia", "Sadaltager", "Sulafat"
]

# Helper function to get user's voice preferences
def get_user_voice_prefs(user_id):
    """Get user's voice preferences from database"""
    prefs = db.tts_voice_prefs.find_one({'user_id': user_id})
    if not prefs:
        # Return default voices
        return {
            'gemini': 'Kore',
            'groq': 'Fritz-PlayAI',
            'cloudflare': None  # Cloudflare doesn't have voice selection
        }
    return prefs.get('voices', {
        'gemini': 'Kore',
        'groq': 'Fritz-PlayAI',
        'cloudflare': None
    })

# Helper function to save wave file
def wave_file(filename, pcm, channels=1, rate=24000, sample_width=2):
    with wave.open(filename, "wb") as wf:
        wf.setnchannels(channels)
        wf.setsampwidth(sample_width)
        wf.setframerate(rate)
        wf.writeframes(pcm)


@Client.on_message(filters.command(['tts', 'speak'], prefixes=['.', '!']) & filters.me)
async def text_to_speech(client, message):
    temp_file = None  # Initialize temp_file at the beginning


    try:
        cmd = message.command
        reply = message.reply_to_message

        # Default provider is Cloudflare
        provider = "cloudflare"
        voice = None
        show_caption = False  # Default: no caption

        # --- Argument Parsing ---
        # Check for provider flag and -v flag
        if len(cmd) > 1:
            if cmd[1].lower() in ["-groq", "-cf", "-cloudflare", "-gemini"]:
                provider = cmd.pop(1).lower().replace("-cf", "cloudflare").strip("-")
            elif cmd[1].lower() == "-v":
                show_caption = True
                cmd.pop(1)

        # Check for voice flag (e.g., voice=Fritz-PlayAI)
        if len(cmd) > 1 and cmd[1].lower().startswith("voice="):
            voice = cmd.pop(1).split("=")[1]

        # Check for -v flag after other arguments
        if len(cmd) > 1 and cmd[1].lower() == "-v":
            show_caption = True
            cmd.pop(1)

        # Get user's voice preferences if no voice specified
        if not voice:
            user_voice_prefs = get_user_voice_prefs(message.from_user.id)
            voice = user_voice_prefs.get(provider)

        # Check if provider is available
        if provider == "cloudflare" and cloudflare_ai is None:
            await message.edit("⚠️ Cloudflare AI client is not initialized.")
            return
        elif provider == "groq" and groq_cli is None:
            await message.edit("⚠️ Groq client is not initialized.")
            return
        elif provider == "gemini" and gemini_cli is None:
            await message.edit("⚠️ Gemini client is not initialized.")
            return

        # --- Text and Language Extraction ---
        text = ""
        lang = 'en'  # Default language
        style_prompt = None  # For controlling speech style/tone

        if reply and (reply.text or reply.caption):
            reply_text = reply.text or reply.caption

            # Validate reply text is not empty
            if not reply_text or reply_text.strip() == "":
                await message.edit("❌ The replied message has no text or caption to convert.")
                return

            # Get remaining command arguments (excluding provider and voice flags)
            remaining_args = []
            for arg in cmd[1:]:
                if arg.lower() in ["-groq", "-cf", "-cloudflare", "-gemini"]:
                    continue  # Skip provider flags
                elif arg.lower() == "-v":
                    continue  # Skip caption flag
                elif arg.lower().startswith("voice="):
                    continue  # Skip voice flag
                else:
                    remaining_args.append(arg)

            command_text = ' '.join(remaining_args)

            # Smart colon handling for replies
            if command_text:
                if command_text.endswith(':'):
                    # Command ends with ":" -> read the replied message
                    text = reply_text
                    # Extract style prompt from command (remove the trailing colon)
                    potential_style = command_text[:-1].strip()
                    if potential_style and len(potential_style.split()) <= 5:
                        style_prompt = potential_style
                elif ':' in command_text and command_text.count(':') == 1:
                    # Command contains ":" -> read text after colon as reply
                    potential_style, reply_content = command_text.split(':', 1)
                    potential_style = potential_style.strip()
                    reply_content = reply_content.strip()

                    if reply_content:
                        text = reply_content
                        if potential_style and len(potential_style.split()) <= 5:
                            style_prompt = potential_style
                    else:
                        # No text after colon -> read replied message with style
                        text = reply_text
                        if potential_style and len(potential_style.split()) <= 5:
                            style_prompt = potential_style
                else:
                    # No colon -> use command text as style prompt, read replied message
                    text = reply_text
                    if command_text and len(command_text.split()) <= 5:
                        style_prompt = command_text
            else:
                # No command text -> just read the replied message
                text = reply_text
        else:
            if len(cmd) < 2:
                await message.edit(
                    "❌ **Usage:**\n"
                    "➤ `.tts [-provider] [-v] [voice=VoiceName] [lang] <text>`\n"
                    "➤ Reply: `.tts [-provider] [-v] [voice=VoiceName] [style:]`\n"
                    "➤ Reply with text: `.tts [-provider] [-v] style: reply text`\n"
                    "➤ Inline style: `.tts -gemini Say cheerfully: Hello world`\n\n"
                    "**Flags:** `-v` (show caption), `-gemini`, `-groq`, `-cloudflare`\n"
                    "**Reply modes:**\n"
                    "• `.tts read cheerfully:` → reads replied message cheerfully\n"
                    "• `.tts say softly: Good night` → says 'Good night' as reply\n\n"
                    "**Voice Management:**\n"
                    "• `.ttsvoices` → List all available voices\n"
                    "• `.ttsvoice gemini Despina` → Set default Gemini voice\n"
                    "• `.ttsvoice groq Celeste-PlayAI` → Set default Groq voice"
                )
                return

            if len(cmd[1]) == 2 and cmd[1].lower() in SUPPORTED_LANGUAGES:
                lang = cmd[1].lower()
                text = ' '.join(cmd[2:])
            else:
                text = ' '.join(cmd[1:])

            # Check if text contains a colon for inline style prompt
            if ':' in text and text.count(':') == 1:
                # Split on colon: "Say cheerfully: Hello world" -> style="Say cheerfully", text="Hello world"
                potential_style, potential_text = text.split(':', 1)
                potential_style = potential_style.strip()
                potential_text = potential_text.strip()

                # Use inline style if it looks like a style instruction (not too long)
                if len(potential_style.split()) <= 5 and potential_text:
                    style_prompt = potential_style
                    text = potential_text

        if not text or text.strip() == "":
            await message.edit(
                "❌ **No text to convert!**\n\n"
                f"**Debug info:**\n"
                f"• Reply exists: {reply is not None}\n"
                f"• Reply text: '{reply.text if reply and reply.text else 'None'}'\n"
                f"• Reply caption: '{reply.caption if reply and reply.caption else 'None'}'\n"
                f"• Command: {cmd}\n"
                f"• Style prompt: '{style_prompt}'\n\n"
                "**Usage:** Reply to a message with text/caption and use:\n"
                "• `.tts -gemini read this:` (reads replied message)\n"
                "• `.tts -gemini say softly: Good night` (custom reply)"
            )
            return

        await message.edit(f"🎵 Generating speech using **{provider.capitalize()}**...")

        # Set appropriate file extension based on provider
        if provider == "gemini":
            temp_file = f"tts_output_{message.id}.wav"
        else:
            temp_file = f"tts_output_{message.id}.mp3"

        # --- Provider Logic ---
        if provider == "cloudflare":
            # Cloudflare uses language codes
            # NOTE: Cloudflare's melotts model does not officially use the lang param.
            # It's included here for future compatibility or if using other CF models.
            response = cloudflare_ai.run_model(
                model="@cf/myshell-ai/melotts",
                messages={"prompt": text}
            )
            if not response.get('success'):
                raise ValueError("Cloudflare API request failed.")
            audio_data = response.get('result', {}).get('audio')
            if not audio_data:
                raise ValueError("No audio data in Cloudflare response.")
            with open(temp_file, "wb") as f:
                f.write(base64.b64decode(audio_data))

        elif provider == "groq":
            # Groq uses different models for Arabic
            model_name = "playai-tts"
            if voice and voice in GROQ_ARABIC_VOICES:
                 model_name = "playai-tts-arabic"
            elif not voice:
                voice = "Fritz-PlayAI" # Default Groq voice
            
            response = groq_cli.audio.speech.create(
                model=model_name, voice=voice, input=text, response_format="mp3"
            )
            response.write_to_file(temp_file)

        elif provider == "gemini":
            # Validate voice or use default
            if not voice or voice not in GEMINI_VOICES:
                voice = "Kore"  # Default Gemini voice

            # If style prompt is provided, format the text with style instructions
            if style_prompt:
                # Format the prompt to control speech style according to Gemini docs
                formatted_text = f"{style_prompt}:\n\"{text}\""
            else:
                formatted_text = text

            # Use the correct Gemini TTS API according to official documentation
            response = gemini_cli.models.generate_content(
                model="gemini-2.5-flash-preview-tts",
                contents=formatted_text,
                config=types.GenerateContentConfig(
                    response_modalities=["AUDIO"],
                    speech_config=types.SpeechConfig(
                        voice_config=types.VoiceConfig(
                            prebuilt_voice_config=types.PrebuiltVoiceConfig(
                                voice_name=voice,
                            )
                        )
                    ),
                )
            )

            # Extract audio data and save to file
            audio_data = response.candidates[0].content.parts[0].inline_data.data
            wave_file(temp_file, audio_data)

        # --- Send Audio and Clean Up ---
        await message.delete()

        # Create caption only if -v flag is used
        caption = None
        if show_caption:
            if style_prompt:
                caption = f"🎭 {style_prompt} • {text[:80]}{'...' if len(text) > 80 else ''}"
            else:
                caption = f"🗣 {text[:100]}{'...' if len(text) > 100 else ''}"

        # Always send as voice message (OGG format)
        if reply:
            await reply.reply_voice(temp_file, caption=caption)
        else:
            # Need to get chat_id for sending a new message
            await client.send_voice(message.chat.id, temp_file, caption=caption)

    except Exception as e:
        await message.edit(f"⚠️ **Error:** `{str(e)}`")
    finally:
        if temp_file and os.path.exists(temp_file):
            os.remove(temp_file)






# from pyrogram import Client, filters
# from helpers import db, cloudflare_ai, groq_cli, gemini_cli
# import base64
# import os
# import json
# from pudb import set_trace

# SUPPORTED_LANGUAGES = ['en', 'fr', 'es', 'de', 'it', 'pt', 'pl', 'tr', 'ru', 'nl', 'cs', 'ar', 'zh', 'ja', 'ko']

# # Groq TTS voices
# GROQ_ENGLISH_VOICES = [
#     'Arista-PlayAI', 'Atlas-PlayAI', 'Basil-PlayAI', 'Briggs-PlayAI',
#     'Calum-PlayAI', 'Celeste-PlayAI', 'Cheyenne-PlayAI', 'Chip-PlayAI',
#     'Cillian-PlayAI', 'Deedee-PlayAI', 'Fritz-PlayAI', 'Gail-PlayAI',
#     'Indigo-PlayAI', 'Mamaw-PlayAI', 'Mason-PlayAI', 'Mikail-PlayAI',
#     'Mitch-PlayAI', 'Quinn-PlayAI', 'Thunder-PlayAI'
# ]

# GROQ_ARABIC_VOICES = [
#     'Ahmad-PlayAI', 'Amira-PlayAI', 'Khalid-PlayAI', 'Nasser-PlayAI'
# ]

# @Client.on_message(filters.command(['tts', 'speak'], prefixes=['.', '!']) & filters.me)
# async def text_to_speech(client, message):
#     try:
#         cmd = message.command
#         reply = message.reply_to_message

#         # Default provider is Cloudflare
#         provider = "cloudflare"
#         voice = None

#         # Check for provider flag
#         if len(cmd) > 1:
#             if cmd[1].lower() == "-groq":
#                 provider = "groq"
#                 cmd.pop(1)  # Remove the provider flag
#                 # Default voice for Groq
#                 voice = "Fritz-PlayAI"
#             elif cmd[1].lower() == "-cf" or cmd[1].lower() == "-cloudflare":
#                 provider = "cloudflare"
#                 cmd.pop(1)  # Remove the provider flag
#             elif cmd[1].lower() == "-gemini":
#                 provider = "gemini"
#                 cmd.pop(1) # Remove the provider flag
#                 voice = "echo-en-us" # Default voice for Gemini

#         # Check if provider is available
#         if provider == "cloudflare" and cloudflare_ai is None:
#             await message.edit("⚠️ Cloudflare AI client is not initialized. Please check your credentials or use other providers.")
#             return
#         elif provider == "groq" and groq_cli is None:
#             await message.edit("⚠️ Groq client is not initialized. Please check your credentials.")
#             return
#         elif provider == "gemini" and gemini_cli is None:
#             await message.edit("⚠️ Gemini client is not initialized. Please check your credentials.")
#             return

#         # Handle voice selection for Groq
#         if provider == "groq" and len(cmd) > 1 and cmd[1].startswith("voice="):
#             voice_name = cmd[1].split("=")[1]
#             if voice_name in GROQ_ENGLISH_VOICES or voice_name in GROQ_ARABIC_VOICES:
#                 voice = voice_name
#                 cmd.pop(1)  # Remove the voice parameter
#             else:
#                 await message.edit(
#                     f"❌ Invalid voice. Available English voices: {', '.join(GROQ_ENGLISH_VOICES[:3])}...\n"
#                     f"Available Arabic voices: {', '.join(GROQ_ARABIC_VOICES)}"
#                 )
#                 return

#         # Handle replied message
#         if reply and (reply.text or reply.caption):
#             text = reply.text or reply.caption
#             # If command has language specified
#             if len(cmd) > 1 and len(cmd[1]) == 2 and cmd[1].lower() in SUPPORTED_LANGUAGES:
#                 lang = cmd[1].lower()
#             else:
#                 lang = 'en'
#         else:
#             # Original direct command handling
#             if len(cmd) < 2:
#                 await message.edit(
#                     "❌ Usage:\n"
#                     "1. Reply to a message: `.tts [-groq|-cf|-gemini] [voice=VoiceName] [lang]`\n"
#                     "2. Direct text: `.tts [-groroq|-cf|-gemini] [voice=VoiceName] [lang] <text>`\n"
#                     f"Supported languages: {', '.join(SUPPORTED_LANGUAGES)}\n"
#                     f"Providers: cloudflare (default), groq, gemini\n"
#                     f"For Groq voices, use voice=VoiceName (e.g., voice=Fritz-PlayAI)"
#                 )
#                 return

#             # Check if first argument is a language code
#             lang = 'en'  # default language
#             if len(cmd[1]) == 2 and cmd[1].lower() in SUPPORTED_LANGUAGES:
#                 lang = cmd[1].lower()
#                 text = ' '.join(cmd[2:])
#             else:
#                 text = ' '.join(cmd[1:])

#         if not text:
#             await message.edit("❌ No text provided to convert to speech.")
#             return

#         await message.edit(f"🎵 Generating speech using {provider.capitalize()}...")

#         temp_file = f"tts_output_{message.id}.wav"

#         if provider == "cloudflare":
#             # Call Cloudflare AI TTS API
#             response = cloudflare_ai.run_model(
#                 model="@cf/myshell-ai/melotts",
#                 messages={
#                     "prompt": text,
#                     "lang": lang
#                 }
#             )

#             if not response.get('success'):
#                 raise ValueError("API request was not successful")

#             # Get base64 audio data - it's directly in the response
#             audio_data = response.get('result', {}).get('audio')
#             if not audio_data:
#                 raise ValueError("No audio data in response")

#             # Save as temporary audio file
#             try:
#                 with open(temp_file, "wb") as f:
#                     f.write(base64.b64decode(audio_data))
#             except Exception as e:
#                 raise ValueError(f"Failed to decode audio data: {str(e)}")

#         elif provider == "groq":
#             # Determine which model to use based on language
#             model = "playai-tts"
#             if lang == "ar":
#                 model = "playai-tts-arabic"
#                 # If no voice specified or voice is not Arabic, use default Arabic voice
#                 if voice not in GROQ_ARABIC_VOICES:
#                     voice = GROQ_ARABIC_VOICES[0]
#             else:
#                 # For non-Arabic, ensure we're using an English voice
#                 if voice not in GROQ_ENGLISH_VOICES:
#                     voice = GROQ_ENGLISH_VOICES[0]

#             # Call Groq TTS API
#             response = groq_cli.audio.speech.create(
#                 model=model,
#                 voice=voice,
#                 input=text,
#                 response_format="wav"
#             )

#             # Save the audio file
#             response.write_to_file(temp_file)

#         elif provider == "gemini":
#             # Call Gemini TTS API
#             response = gemini_cli.generate_content(
#                 f"Speak with a {voice} voice: {text}",
#                 generation_config=dict(
#                     temperature=0,
#                 ),
#                 stream=True,
#             )

#             # Save the audio file
#             with open(temp_file, "wb") as f:
#                 for chunk in response:
#                     f.write(chunk.audio_content)

#         # Send audio file
#         await message.delete()

#         # If the original message was a reply, maintain the reply context
#         if reply:
#             await reply.reply_voice(temp_file, caption=f"🗣 {text[:100]}{'...' if len(text) > 100 else ''}")
#         else:
#             await message.reply_voice(temp_file, caption=f"🗣 {text[:100]}{'...' if len(text) > 100 else ''}")

#         # Clean up
#         os.remove(temp_file)

#     except Exception as e:
#         error_msg = str(e)
#         if len(error_msg) > 100:
#             error_msg = error_msg[:100] + "..."
#         await message.edit(f"⚠️ Error generating speech: {error_msg}")
