# plugins/crypto_plug.py
import requests
import json
import asyncio
from datetime import datetime, timedelta
from pyrogram import Client, filters
from helpers import db
from helpers.prompts import PROMPTS
from helpers.ai_utils import AIResponseGenerator, parse_provider
from pyrogram.types import Message
from helpers.config import config
from pudb import set_trace

# ====== CONFIG ======
CMC_API_KEY = "YOUR_CMC_API_KEY"  # You'll need to set this in your config
SYMBOL = "BTC"  # Default symbol (BTC, ETH, ...)
CANDLE_DATA = []  
# ====================

def get_cmc_api_key():
    """Get CoinMarketCap API key from config or environment"""
    api_key = config.get_coinmarketcap_api_key()
    if not api_key:
        raise ValueError("CoinMarketCap API key not found. Please set COINMARKETCAP_API_KEY in your .env file or use .cryptokey command")
    return api_key

def get_cmc_data(symbol):
    """Fetch cryptocurrency data from CoinMarketCap API"""
    api_key = get_cmc_api_key()

    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"
    params = {"symbol": symbol.upper(), "convert": "USD"}
    headers = {"X-CMC_PRO_API_KEY": api_key}

    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()
    return response.json()

def get_sentiment_emoji(change_24h):
    """Get appropriate emoji based on 24h price change"""
    if change_24h > 5:
        return "🚀"  # Moon
    elif change_24h > 2:
        return "📈"  # Chart up
    elif change_24h > 0:
        return "⬆️"  # Up arrow
    elif change_24h > -2:
        return "⬇️"  # Down arrow
    elif change_24h > -5:
        return "📉"  # Chart down
    else:
        return "💥"  # Crash

def format_large_number(num):
    """Format large numbers in a readable way"""
    if num >= 1e12:
        return f"${num/1e12:.2f}T"
    elif num >= 1e9:
        return f"${num/1e9:.2f}B"
    elif num >= 1e6:
        return f"${num/1e6:.2f}M"
    elif num >= 1e3:
        return f"${num/1e3:.2f}K"
    else:
        return f"${num:.2f}"

def get_coingecko_id(symbol):
    """Get CoinGecko ID for a symbol"""
    # Common symbol to CoinGecko ID mapping
    symbol_map = {
        'BTC': 'bitcoin',
        'ETH': 'ethereum',
        'ADA': 'cardano',
        'DOT': 'polkadot',
        'LINK': 'chainlink',
        'LTC': 'litecoin',
        'XRP': 'ripple',
        'BCH': 'bitcoin-cash',
        'BNB': 'binancecoin',
        'SOL': 'solana',
        'MATIC': 'matic-network',
        'AVAX': 'avalanche-2',
        'ATOM': 'cosmos',
        'DOGE': 'dogecoin',
        'SHIB': 'shiba-inu',
        'UNI': 'uniswap',
        'AAVE': 'aave',
        'SUSHI': 'sushi',
        'COMP': 'compound-governance-token',
        'MKR': 'maker'
    }

    return symbol_map.get(symbol.upper(), symbol.lower())

def get_bitcoin_dominance_data():
    """Fetch Bitcoin dominance data from multiple sources with historical analysis"""
    try:
        dominance_data = {}

        # Try CoinGecko global data first
        try:
            url = "https://api.coingecko.com/api/v3/global"
            headers = {"User-Agent": "CryptoAnalysisBot/1.0"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            global_data = response.json()

            if 'data' in global_data:
                data = global_data['data']
                dominance_data.update({
                    "btc_dominance": data.get('market_cap_percentage', {}).get('btc'),
                    "eth_dominance": data.get('market_cap_percentage', {}).get('eth'),
                    "total_market_cap": data.get('total_market_cap', {}).get('usd'),
                    "total_volume_24h": data.get('total_volume', {}).get('usd'),
                    "active_cryptocurrencies": data.get('active_cryptocurrencies'),
                    "markets": data.get('markets'),
                    "market_cap_change_24h": data.get('market_cap_change_percentage_24h_usd')
                })

        except Exception as e:
            print(f"Warning: Could not fetch global data: {str(e)}")

        # Get extended historical dominance data for better technical analysis
        try:
            # Get Bitcoin market cap history for dominance calculation
            btc_url = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
            params = {"vs_currency": "usd", "days": "90", "interval": "daily"}

            response = requests.get(btc_url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            btc_data = response.json()

            # Get total market cap history (approximated)
            global_url = "https://api.coingecko.com/api/v3/global"
            global_response = requests.get(global_url, headers=headers, timeout=10)
            global_response.raise_for_status()
            current_global = global_response.json()

            if 'market_caps' in btc_data and btc_data['market_caps']:
                # Calculate historical dominance percentages
                btc_caps = btc_data['market_caps']
                current_total_cap = current_global.get('data', {}).get('total_market_cap', {}).get('usd', 0)
                current_btc_cap = btc_caps[-1][1] if btc_caps else 0
                current_dominance = (current_btc_cap / current_total_cap * 100) if current_total_cap > 0 else 0

                # Estimate historical total market caps based on current ratio
                historical_dominance = []
                for timestamp, btc_cap in btc_caps[-30:]:  # Last 30 days
                    # Rough estimation of total market cap based on current dominance ratio
                    estimated_total_cap = btc_cap / (current_dominance / 100) if current_dominance > 0 else 0
                    estimated_dominance = (btc_cap / estimated_total_cap * 100) if estimated_total_cap > 0 else 0

                    historical_dominance.append({
                        "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d"),
                        "timestamp": timestamp,
                        "btc_market_cap": btc_cap,
                        "estimated_total_cap": estimated_total_cap,
                        "dominance_percentage": round(estimated_dominance, 2)
                    })

                dominance_data['historical_dominance'] = historical_dominance
                dominance_data['dominance_trend_data'] = {
                    "current": current_dominance,
                    "7d_ago": historical_dominance[-7]['dominance_percentage'] if len(historical_dominance) >= 7 else current_dominance,
                    "30d_ago": historical_dominance[0]['dominance_percentage'] if historical_dominance else current_dominance
                }

        except Exception as e:
            print(f"Warning: Could not fetch historical dominance data: {str(e)}")

        return dominance_data

    except Exception as e:
        print(f"Warning: Could not fetch Bitcoin dominance data: {str(e)}")
        return {}

def get_extended_market_data(symbol):
    """Fetch extended market data from CoinGecko API"""
    try:
        coingecko_id = get_coingecko_id(symbol)

        # Get detailed coin information
        url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}"
        params = {
            "localization": "false",
            "tickers": "false",
            "market_data": "true",
            "community_data": "false",
            "developer_data": "false",
            "sparkline": "false"
        }

        headers = {"User-Agent": "CryptoAnalysisBot/1.0"}
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()

        market_data = data.get('market_data', {})

        extended_data = {
            "ath": market_data.get('ath', {}).get('usd'),
            "ath_change_percentage": market_data.get('ath_change_percentage', {}).get('usd'),
            "atl": market_data.get('atl', {}).get('usd'),
            "atl_change_percentage": market_data.get('atl_change_percentage', {}).get('usd'),
            "circulating_supply": market_data.get('circulating_supply'),
            "total_supply": market_data.get('total_supply'),
            "max_supply": market_data.get('max_supply'),
            "market_cap_rank": market_data.get('market_cap_rank'),
            "price_change_percentage_30d": market_data.get('price_change_percentage_30d'),
            "price_change_percentage_60d": market_data.get('price_change_percentage_60d'),
            "price_change_percentage_200d": market_data.get('price_change_percentage_200d'),
            "price_change_percentage_1y": market_data.get('price_change_percentage_1y')
        }

        return extended_data

    except Exception as e:
        print(f"Warning: Could not fetch extended market data for {symbol}: {str(e)}")
        return {}

def get_candlestick_data(symbol, days=7):
    """Fetch candlestick data from CoinGecko API (free, no API key required)"""
    try:
        coingecko_id = get_coingecko_id(symbol)

        # CoinGecko OHLC endpoint (free)
        url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}/ohlc"
        params = {
            "vs_currency": "usd",
            "days": days
        }

        # Add headers to be more respectful to the API
        headers = {
            "User-Agent": "CryptoAnalysisBot/1.0"
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        ohlc_data = response.json()

        # Convert to our format and include volume estimation
        candles = []
        for i, ohlc in enumerate(ohlc_data[-30:]):  # Get more data for better analysis
            timestamp, open_price, high, low, close = ohlc

            # Estimate volume based on price movement (placeholder)
            price_range = high - low
            estimated_volume = price_range * close * 1000000  # Rough estimation

            candles.append({
                "timestamp": timestamp,
                "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d %H:%M"),
                "open": round(open_price, 8),
                "high": round(high, 8),
                "low": round(low, 8),
                "close": round(close, 8),
                "volume": round(estimated_volume, 2),
                "price_change": round(close - open_price, 8),
                "price_change_pct": round(((close - open_price) / open_price) * 100, 4) if open_price > 0 else 0
            })

        return candles

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 429:
            print(f"Rate limit reached for {symbol} candlestick data. Trying alternative approach...")
            # Try with fewer days if rate limited
            try:
                params["days"] = 3
                response = requests.get(url, params=params, headers=headers, timeout=10)
                response.raise_for_status()
                ohlc_data = response.json()

                candles = []
                for ohlc in ohlc_data[-15:]:  # Get fewer candles
                    timestamp, open_price, high, low, close = ohlc
                    price_range = high - low
                    estimated_volume = price_range * close * 1000000

                    candles.append({
                        "timestamp": timestamp,
                        "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d %H:%M"),
                        "open": round(open_price, 8),
                        "high": round(high, 8),
                        "low": round(low, 8),
                        "close": round(close, 8),
                        "volume": round(estimated_volume, 2),
                        "price_change": round(close - open_price, 8),
                        "price_change_pct": round(((close - open_price) / open_price) * 100, 4) if open_price > 0 else 0
                    })

                print(f"Successfully fetched {len(candles)} candles for {symbol} with reduced timeframe")
                return candles

            except Exception as fallback_error:
                print(f"Fallback also failed for {symbol}: {str(fallback_error)}")
                return []
        else:
            print(f"HTTP error fetching candlestick data for {symbol}: {str(e)}")
        return []
    except Exception as e:
        print(f"Warning: Could not fetch candlestick data for {symbol}: {str(e)}")
        return []

def create_fallback_candles(current_price, change_24h, change_7d):
    """Create synthetic candlestick data when API fails"""
    try:
        # Generate 20 synthetic candles based on price changes
        candles = []
        base_price = current_price / (1 + change_24h/100)  # Approximate yesterday's price

        for i in range(20):
            # Create realistic OHLC data with some randomness
            progress = i / 19  # 0 to 1
            price_trend = base_price * (1 + (change_24h/100) * progress)

            # Add some volatility (±2%)
            volatility = price_trend * 0.02
            open_price = price_trend + (volatility * (0.5 - (i % 3) / 3))
            close_price = price_trend + (volatility * (0.5 - ((i+1) % 3) / 3))
            high_price = max(open_price, close_price) * (1 + 0.01)
            low_price = min(open_price, close_price) * (1 - 0.01)

            candles.append({
                "timestamp": int((datetime.now().timestamp() - (19-i) * 3600) * 1000),
                "date": (datetime.now() - timedelta(hours=19-i)).strftime("%Y-%m-%d %H:%M"),
                "open": round(open_price, 8),
                "high": round(high_price, 8),
                "low": round(low_price, 8),
                "close": round(close_price, 8),
                "volume": round(abs(close_price - open_price) * 1000000, 2),
                "price_change": round(close_price - open_price, 8),
                "price_change_pct": round(((close_price - open_price) / open_price) * 100, 4) if open_price > 0 else 0
            })

        print(f"Generated {len(candles)} synthetic candles as fallback")
        return candles

    except Exception as e:
        print(f"Error creating fallback candles: {e}")
        return []

def calculate_dominance_support_resistance(historical_dominance):
    """Calculate sophisticated support and resistance levels for Bitcoin dominance"""
    if not historical_dominance or len(historical_dominance) < 10:
        return {}

    try:
        # Extract dominance percentages
        dominance_values = [float(d['dominance_percentage']) for d in historical_dominance if d['dominance_percentage']]

        if not dominance_values:
            return {}

        current_dominance = dominance_values[-1]

        # Calculate key levels
        levels = {}

        # Recent highs and lows (last 30 days)
        recent_high = max(dominance_values[-30:]) if len(dominance_values) >= 30 else max(dominance_values)
        recent_low = min(dominance_values[-30:]) if len(dominance_values) >= 30 else min(dominance_values)

        # Fibonacci-like levels based on recent range
        range_size = recent_high - recent_low

        levels.update({
            'recent_high': round(recent_high, 2),
            'recent_low': round(recent_low, 2),
            'range_size': round(range_size, 2),

            # Key resistance levels (above current)
            'resistance_1': round(current_dominance + (range_size * 0.236), 2),
            'resistance_2': round(current_dominance + (range_size * 0.382), 2),
            'resistance_3': round(current_dominance + (range_size * 0.618), 2),
            'major_resistance': round(recent_high, 2),

            # Key support levels (below current)
            'support_1': round(current_dominance - (range_size * 0.236), 2),
            'support_2': round(current_dominance - (range_size * 0.382), 2),
            'support_3': round(current_dominance - (range_size * 0.618), 2),
            'major_support': round(recent_low, 2),

            # Psychological levels (round numbers)
            'psychological_levels': []
        })

        # Find psychological levels (round numbers near current price)
        for level in [55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70]:
            if abs(level - current_dominance) <= 5:  # Within 5% range
                levels['psychological_levels'].append(level)

        # Calculate trend strength
        if len(dominance_values) >= 7:
            week_ago = dominance_values[-7]
            trend_strength = ((current_dominance - week_ago) / week_ago) * 100
            levels['trend_strength_7d'] = round(trend_strength, 3)

        if len(dominance_values) >= 30:
            month_ago = dominance_values[-30]
            trend_strength_30d = ((current_dominance - month_ago) / month_ago) * 100
            levels['trend_strength_30d'] = round(trend_strength_30d, 3)

        # Volatility analysis
        if len(dominance_values) >= 14:
            recent_values = dominance_values[-14:]
            avg_dominance = sum(recent_values) / len(recent_values)
            variance = sum([(x - avg_dominance) ** 2 for x in recent_values]) / len(recent_values)
            volatility = (variance ** 0.5) / avg_dominance * 100
            levels['volatility_14d'] = round(volatility, 3)

        return levels

    except Exception as e:
        print(f"Error calculating dominance support/resistance: {e}")
        return {}

def calculate_technical_indicators(candles):
    """Calculate technical indicators from candlestick data"""
    if not candles or len(candles) < 5:  # Reduced minimum requirement
        return {}

    closes = [float(c['close']) for c in candles]
    highs = [float(c['high']) for c in candles]
    lows = [float(c['low']) for c in candles]

    indicators = {}

    try:
        # Simple Moving Averages
        if len(closes) >= 20:
            indicators['sma_20'] = sum(closes[-20:]) / 20
        if len(closes) >= 10:
            indicators['sma_10'] = sum(closes[-10:]) / 10

        # Exponential Moving Averages (simplified calculation)
        if len(closes) >= 20:
            multiplier_20 = 2 / (20 + 1)
            ema_20 = closes[0]
            for price in closes[1:]:
                ema_20 = (price * multiplier_20) + (ema_20 * (1 - multiplier_20))
            indicators['ema_20'] = ema_20

        if len(closes) >= 12:
            multiplier_12 = 2 / (12 + 1)
            ema_12 = closes[0]
            for price in closes[1:]:
                ema_12 = (price * multiplier_12) + (ema_12 * (1 - multiplier_12))
            indicators['ema_12'] = ema_12

        # RSI Calculation
        if len(closes) >= 14:
            gains = []
            losses = []
            for i in range(1, len(closes)):
                change = closes[i] - closes[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = sum(gains[-14:]) / 14
            avg_loss = sum(losses[-14:]) / 14

            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                indicators['rsi_14'] = rsi

        # Bollinger Bands
        if len(closes) >= 20:
            sma_20 = indicators.get('sma_20', sum(closes[-20:]) / 20)
            variance = sum([(price - sma_20) ** 2 for price in closes[-20:]]) / 20
            std_dev = variance ** 0.5
            indicators['bollinger_upper'] = sma_20 + (2 * std_dev)
            indicators['bollinger_lower'] = sma_20 - (2 * std_dev)
            indicators['bollinger_middle'] = sma_20

        # Support and Resistance levels
        recent_highs = highs[-10:] if len(highs) >= 10 else highs
        recent_lows = lows[-10:] if len(lows) >= 10 else lows

        indicators['resistance_level'] = max(recent_highs)
        indicators['support_level'] = min(recent_lows)

        # Price action analysis
        current_price = closes[-1]
        prev_price = closes[-2] if len(closes) >= 2 else current_price

        indicators['price_momentum'] = ((current_price - prev_price) / prev_price) * 100
        indicators['volatility'] = (max(recent_highs) - min(recent_lows)) / min(recent_lows) * 100

    except Exception as e:
        print(f"Error calculating indicators: {e}")

    return indicators

def build_json(symbol, cmc_data, candles=None):
    """Build JSON format compatible with the crypto analysis prompt"""
    try:
        quote = cmc_data["data"][symbol.upper()]["quote"]["USD"]

        # Calculate technical indicators if candles are available
        indicators = calculate_technical_indicators(candles) if candles else {}

        market_json = {
            "symbol": symbol.upper(),
            "current_price": quote["price"],
            "percent_change_1h": quote["percent_change_1h"],
            "percent_change_24h": quote["percent_change_24h"],
            "percent_change_7d": quote["percent_change_7d"],
            "volume_24h": quote["volume_24h"],
            "market_cap": quote["market_cap"],
            "candles": candles or [],
            "technical_indicators": indicators,
            "data_quality": {
                "candles_count": len(candles) if candles else 0,
                "indicators_available": len(indicators) > 0,
                "analysis_confidence": "high" if len(candles) >= 20 else "medium" if len(candles) >= 10 else "low"
            }
        }
        return market_json
    except KeyError as e:
        raise ValueError(f"Invalid response format or symbol not found: {e}")

@Client.on_message(filters.me & filters.command("crypto", prefixes=[".", "!"]))
async def crypto_analysis(client: Client, message: Message):
    """Handle cryptocurrency analysis command"""
    try:
        cmd = message.command
        
        # Show help if no arguments
        if len(cmd) < 2:
            help_text = (
                "**Cryptocurrency Analysis Commands:**\n\n"
                "**Usage:** `.crypto <symbol> [provider] [language]`\n\n"
                "**Examples:**\n"
                "- `.crypto BTC`: Analyze Bitcoin with default provider\n"
                "- `.crypto BTC -g`: Analyze Bitcoin with Gemini\n"
                "- `.crypto -g BTC -fa`: Analyze Bitcoin with Gemini in Persian\n"
                "- `.crypto ETH -q -en`: Analyze Ethereum with Groq in English\n"
                "- `.crypto ADA -c -ar`: Analyze Cardano with Cloudflare in Arabic\n\n"
                "**Providers:**\n"
                "- `-g`: Google/Gemini\n"
                "- `-q`: Groq\n"
                "- `-o`: Ollama\n"
                "- `-r`: OpenRouter\n"
                "- `-c`: Cloudflare\n\n"
                "**Languages:**\n"
                "- `-fa`: Persian/Farsi\n"
                "- `-en`: English\n"
                "- `-ar`: Arabic\n"
                "- `-es`: Spanish\n"
                "- `-fr`: French\n"
                "- `-de`: German\n"
                "- `-it`: Italian\n"
                "- `-pt`: Portuguese\n"
                "- `-ru`: Russian\n"
                "- `-zh`: Chinese\n"
                "- `-ja`: Japanese\n"
                "- `-ko`: Korean\n\n"
                "**User-Friendly Analysis Includes:**\n"
                "- 📊 Quick summary with clear sentiment\n"
                "- 💰 Price performance in simple terms\n"
                "- 📈 Technical analysis with explanations\n"
                "- 🎯 Specific trading recommendations\n"
                "- ⚠️ Risk factors and what to watch\n"
                "- 💡 Bottom line advice for your level\n\n"
                "**Data Sources:**\n"
                "- Real-time: CoinMarketCap | Historical: CoinGecko\n"
                "- 30 candlesticks + 12+ technical indicators\n\n"
                "**Setup:** Add `COINMARKETCAP_API_KEY=your_key` to your .env file\n"
                "Get your free API key from: https://coinmarketcap.com/api/"
            )
            await message.edit(help_text)
            return
        
        # Parse symbol, provider, and language - handle various orders
        symbol = None
        provider_args = []
        language = None

        # Language mapping
        language_map = {
            '-fa': 'persian',
            '-en': 'english',
            '-ar': 'arabic',
            '-es': 'spanish',
            '-fr': 'french',
            '-de': 'german',
            '-it': 'italian',
            '-pt': 'portuguese',
            '-ru': 'russian',
            '-zh': 'chinese',
            '-ja': 'japanese',
            '-ko': 'korean'
        }

        # Find the symbol, provider flags, and language flags
        for arg in cmd[1:]:
            if arg in language_map:
                language = language_map[arg]
            elif arg.startswith('-'):
                provider_args.append(arg)
            else:
                symbol = arg.upper()

        if not symbol:
            await message.edit("❌ Please specify a cryptocurrency symbol (e.g., BTC, ETH, ADA)")
            return

        provider = parse_provider(provider_args)

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Show loading message
        await message.edit(f"📊 Fetching {symbol} data from CoinMarketCap...")

        try:
            # Fetch cryptocurrency data from CoinMarketCap
            cmc_data = get_cmc_data(symbol)

            # Fetch candlestick data from CoinGecko
            await message.edit(f"📊 Fetching {symbol} candlestick data...")
            candles = get_candlestick_data(symbol, days=14)  # Get more days for better analysis

            # If no candles, create fallback data
            if not candles:
                await message.edit(f"📊 Creating synthetic data for {symbol}...")
                current_price = cmc_data["data"][symbol.upper()]["quote"]["USD"]["price"]
                change_24h = cmc_data["data"][symbol.upper()]["quote"]["USD"]["percent_change_24h"]
                change_7d = cmc_data["data"][symbol.upper()]["quote"]["USD"]["percent_change_7d"]
                candles = create_fallback_candles(current_price, change_24h, change_7d)

            # Fetch extended market data
            await message.edit(f"📊 Fetching {symbol} extended market data...")
            extended_data = get_extended_market_data(symbol)

            # Debug: Check what data we actually got
            print(f"DEBUG: {symbol} - Candles: {len(candles)}, Extended: {bool(extended_data)}")

            # Update status based on data availability
            if len(candles) >= 20:
                data_quality = "comprehensive"
                status_msg = f"📈 Analyzing {symbol} (comprehensive data, {len(candles)} candles)..."
            elif len(candles) >= 10:
                data_quality = "standard"
                status_msg = f"📈 Analyzing {symbol} (standard data, {len(candles)} candles)..."
            elif len(candles) >= 5:
                data_quality = "basic"
                status_msg = f"📈 Analyzing {symbol} (basic data, {len(candles)} candles)..."
            else:
                data_quality = "minimal"
                status_msg = f"📈 Analyzing {symbol} (price data only)..."

            await message.edit(status_msg)

            # Build comprehensive market data
            market_json = build_json(symbol, cmc_data, candles)

            # Add extended market data
            if extended_data:
                market_json["extended_market_data"] = extended_data

            # Prepare the data for AI analysis
            json_data = json.dumps(market_json, indent=2)

            # Create a comprehensive analysis query
            data_summary = []
            indicators = market_json.get('technical_indicators', {})

            if candles:
                candle_type = "real-time" if len(candles) >= 15 else "synthetic"
                data_summary.append(f"✅ {len(candles)} OHLC candlesticks ({candle_type} data)")

            if indicators:
                indicator_list = []
                if 'rsi_14' in indicators:
                    indicator_list.append(f"RSI: {indicators['rsi_14']:.1f}")
                if 'sma_20' in indicators:
                    indicator_list.append(f"SMA20: ${indicators['sma_20']:.2f}")
                if 'support_level' in indicators:
                    indicator_list.append(f"Support: ${indicators['support_level']:.2f}")
                if 'resistance_level' in indicators:
                    indicator_list.append(f"Resistance: ${indicators['resistance_level']:.2f}")

                if indicator_list:
                    data_summary.append(f"✅ Technical indicators: {', '.join(indicator_list)}")

            if extended_data:
                data_summary.append(f"✅ Extended market data (ATH/ATL, supply metrics)")

            data_info = "\n".join(data_summary) if data_summary else "⚠️ Limited data available"

            # Create more specific instructions based on available data
            if len(candles) >= 15 and indicators:
                analysis_instruction = "COMPREHENSIVE ANALYSIS: Use all provided technical indicators and candlestick data for detailed analysis."
            elif len(candles) >= 5:
                analysis_instruction = "STANDARD ANALYSIS: Use available candlestick data and basic indicators."
            else:
                analysis_instruction = "BASIC ANALYSIS: Focus on price trends and percentage changes from CoinMarketCap data."

            query = f"""Analyze this cryptocurrency data for {symbol}:

DATA AVAILABLE:
{data_info}

{analysis_instruction}

REQUIREMENTS:
- Use ONLY the provided data - don't estimate missing indicators
- If technical_indicators are provided, use those exact values
- Provide specific price levels for entry/exit based on available support/resistance
- Give clear buy/sell/hold recommendation
- Explain your reasoning in simple terms

```json
{json_data}
```

Provide actionable trading analysis with specific recommendations."""

            # Update status
            await message.edit(f"🤖 Analyzing {symbol} with AI...")

            # Get AI analysis using crypto_analyst prompt
            ai_generator = AIResponseGenerator(provider, model)
            system_prompt = PROMPTS.get('crypto_analyst', "You are a cryptocurrency analyst.")

            # Add language instruction if specified
            if language:
                if language == 'persian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Persian (Farsi). Use proper Persian financial terminology. Keep the friendly, conversational tone while using appropriate Persian expressions. Use Persian numbers and currency terms."
                elif language == 'arabic':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Arabic. Use proper Arabic financial terminology. Keep the friendly, conversational tone while using appropriate Arabic expressions."
                elif language == 'spanish':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Spanish. Use proper Spanish financial terminology. Keep the friendly, conversational tone while using appropriate Spanish expressions."
                elif language == 'french':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent French. Use proper French financial terminology. Keep the friendly, conversational tone while using appropriate French expressions."
                elif language == 'german':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent German. Use proper German financial terminology. Keep the friendly, conversational tone while using appropriate German expressions."
                elif language == 'italian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Italian. Use proper Italian financial terminology. Keep the friendly, conversational tone while using appropriate Italian expressions."
                elif language == 'portuguese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Portuguese. Use proper Portuguese financial terminology. Keep the friendly, conversational tone while using appropriate Portuguese expressions."
                elif language == 'russian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Russian. Use proper Russian financial terminology. Keep the friendly, conversational tone while using appropriate Russian expressions."
                elif language == 'chinese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Chinese (Simplified). Use proper Chinese financial terminology. Keep the friendly, conversational tone while using appropriate Chinese expressions."
                elif language == 'japanese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Japanese. Use proper Japanese financial terminology. Keep the friendly, conversational tone while using appropriate Japanese expressions."
                elif language == 'korean':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Korean. Use proper Korean financial terminology. Keep the friendly, conversational tone while using appropriate Korean expressions."
                else:  # english or default
                    system_prompt += "\n\nIMPORTANT: Respond in fluent English. Use proper English financial terminology. Keep the friendly, conversational tone."
            
            response_text = await ai_generator.generate_response(
                system_prompt=system_prompt,
                user_query=query,
                temperature=0.7,
                max_tokens=3000,
                use_search=False
            )
            
            # Format the response with better structure
            current_price = market_json.get('current_price', 0)
            change_24h = market_json.get('percent_change_24h', 0)
            volume_24h = market_json.get('volume_24h', 0)
            market_cap = market_json.get('market_cap', 0)

            # Get appropriate emoji based on performance
            sentiment_emoji = get_sentiment_emoji(change_24h)

            # Format header with better info
            header = f"🪙 **{symbol} Crypto Analysis** {sentiment_emoji}\n"
            header += f"💲 **${current_price:.4f}** ({change_24h:+.2f}% 24h)\n"
            header += f"📊 Volume: {format_large_number(volume_24h)} | Cap: {format_large_number(market_cap)}\n"
            header += f"⏰ *Analysis at {datetime.now().strftime('%H:%M')}*\n"
            header += "─" * 40 + "\n\n"

            final_response = header + response_text

            # Add footer with data quality info
            data_quality = market_json.get('data_quality', {})
            candles_count = data_quality.get('candles_count', 0)
            confidence = data_quality.get('analysis_confidence', 'medium')

            footer = f"\n\n📊 **Analysis Info:**\n"
            footer += f"• Data points: {candles_count} candlesticks\n"
            footer += f"• Confidence: {confidence.title()}\n"
            footer += f"• Sources: CoinMarketCap + CoinGecko\n"
            footer += "⚠️ *Not financial advice. DYOR.*"

            final_response += footer

            # Send response in chunks if necessary
            chunks = [
                final_response[i : i + 4096] for i in range(0, len(final_response), 4096)
            ]

            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)
                    
        except ValueError as e:
            await message.edit(f"❌ Error: {str(e)}")
        except requests.exceptions.RequestException as e:
            await message.edit(f"❌ Network error: {str(e)}")
        except Exception as e:
            await message.edit(f"❌ Unexpected error: {str(e)}")
            
    except Exception as e:
        await message.edit(f"❌ Command error: {str(e)}")

@Client.on_message(filters.me & filters.command("btcd", prefixes=[".", "!"]))
async def bitcoin_dominance_analysis(client: Client, message: Message):
    """Handle Bitcoin dominance analysis command"""
    try:
        cmd = message.command

        # Show help if requested
        if len(cmd) > 1 and cmd[1].lower() in ['help', '-h', '--help']:
            help_text = (
                "**Bitcoin Dominance Analysis Commands:**\n\n"
                "**Usage:** `.btcd [provider] [language]`\n\n"
                "**Examples:**\n"
                "- `.btcd`: Analyze BTC dominance with default provider\n"
                "- `.btcd -g`: Analyze with Gemini\n"
                "- `.btcd -g -fa`: Analyze with Gemini in Persian\n"
                "- `.btcd -q -en`: Analyze with Groq in English\n\n"
                "**Providers:**\n"
                "- `-g`: Google/Gemini\n"
                "- `-q`: Groq\n"
                "- `-o`: Ollama\n"
                "- `-r`: OpenRouter\n"
                "- `-c`: Cloudflare\n\n"
                "**Languages:**\n"
                "- `-fa`: Persian/Farsi\n"
                "- `-en`: English\n"
                "- `-ar`: Arabic\n"
                "- And more...\n\n"
                "**Analysis Includes:**\n"
                "- Current BTC dominance percentage\n"
                "- Historical dominance trends\n"
                "- Market implications and altcoin impact\n"
                "- Trading opportunities based on dominance\n"
                "- Market cycle analysis\n\n"
                "**Data Sources:** CoinGecko Global Market Data"
            )
            await message.edit(help_text)
            return

        # Parse provider and language flags
        provider_args = []
        language = None

        # Language mapping
        language_map = {
            '-fa': 'persian',
            '-en': 'english',
            '-ar': 'arabic',
            '-es': 'spanish',
            '-fr': 'french',
            '-de': 'german',
            '-it': 'italian',
            '-pt': 'portuguese',
            '-ru': 'russian',
            '-zh': 'chinese',
            '-ja': 'japanese',
            '-ko': 'korean'
        }

        # Parse arguments
        for arg in cmd[1:]:
            if arg in language_map:
                language = language_map[arg]
            elif arg.startswith('-'):
                provider_args.append(arg)

        provider = parse_provider(provider_args)

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Show loading message
        await message.edit("📊 Fetching Bitcoin dominance data...")

        try:
            # Fetch Bitcoin dominance data
            dominance_data = get_bitcoin_dominance_data()

            # Calculate sophisticated dominance support/resistance levels
            await message.edit("📊 Calculating dominance support & resistance levels...")
            dominance_levels = {}
            if dominance_data.get('historical_dominance'):
                dominance_levels = calculate_dominance_support_resistance(dominance_data['historical_dominance'])

            # Fetch Bitcoin price data for context
            await message.edit("📊 Fetching Bitcoin price data...")
            btc_cmc_data = get_cmc_data("BTC")
            btc_candles = get_candlestick_data("BTC", days=30)

            # If no candles, create fallback data
            if not btc_candles:
                current_price = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["price"]
                change_24h = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_24h"]
                change_7d = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_7d"]
                btc_candles = create_fallback_candles(current_price, change_24h, change_7d)

            # Calculate Bitcoin technical indicators
            btc_indicators = calculate_technical_indicators(btc_candles)

            # Build comprehensive dominance analysis data
            dominance_analysis = {
                "analysis_type": "bitcoin_dominance",
                "timestamp": datetime.now().isoformat(),
                "dominance_data": dominance_data,
                "dominance_technical_levels": dominance_levels,
                "bitcoin_price_data": {
                    "current_price": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["price"],
                    "percent_change_1h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_1h"],
                    "percent_change_24h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_24h"],
                    "percent_change_7d": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_7d"],
                    "volume_24h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["volume_24h"],
                    "market_cap": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["market_cap"]
                },
                "bitcoin_technical_indicators": btc_indicators,
                "bitcoin_candles": btc_candles[-10:] if btc_candles else [],  # Last 10 candles for context
                "data_quality": {
                    "dominance_data_available": bool(dominance_data),
                    "dominance_levels_available": bool(dominance_levels),
                    "btc_candles_count": len(btc_candles) if btc_candles else 0,
                    "btc_indicators_available": bool(btc_indicators),
                    "analysis_confidence": "high" if dominance_data and dominance_levels and btc_candles else "medium" if dominance_data else "low"
                }
            }

            # Update status
            data_quality = dominance_analysis["data_quality"]["analysis_confidence"]
            await message.edit(f"🔍 Analyzing Bitcoin dominance ({data_quality} confidence)...")

            # Prepare the data for AI analysis
            json_data = json.dumps(dominance_analysis, indent=2)

            # Create dominance-specific analysis query
            current_dominance = dominance_data.get('btc_dominance', 'N/A')
            eth_dominance = dominance_data.get('eth_dominance', 'N/A')

            # Build detailed level information for the query
            levels_info = ""
            if dominance_levels:
                levels_info = f"""
TECHNICAL LEVELS ANALYSIS:
- Current Dominance: {current_dominance}%
- Recent High: {dominance_levels.get('recent_high', 'N/A')}%
- Recent Low: {dominance_levels.get('recent_low', 'N/A')}%
- Major Resistance: {dominance_levels.get('major_resistance', 'N/A')}%
- Resistance Levels: {dominance_levels.get('resistance_1', 'N/A')}%, {dominance_levels.get('resistance_2', 'N/A')}%, {dominance_levels.get('resistance_3', 'N/A')}%
- Major Support: {dominance_levels.get('major_support', 'N/A')}%
- Support Levels: {dominance_levels.get('support_1', 'N/A')}%, {dominance_levels.get('support_2', 'N/A')}%, {dominance_levels.get('support_3', 'N/A')}%
- Psychological Levels: {', '.join([f'{level}%' for level in dominance_levels.get('psychological_levels', [])])}
- 7-Day Trend: {dominance_levels.get('trend_strength_7d', 'N/A')}%
- 30-Day Trend: {dominance_levels.get('trend_strength_30d', 'N/A')}%
- Volatility (14d): {dominance_levels.get('volatility_14d', 'N/A')}%"""

            query = f"""Analyze this comprehensive Bitcoin dominance data with detailed technical analysis:

CURRENT MARKET STATE:
- Bitcoin Dominance: {current_dominance}%
- Ethereum Dominance: {eth_dominance}%
- Total Market Cap: {format_large_number(dominance_data.get('total_market_cap', 0))}
{levels_info}

DATA AVAILABLE:
✅ Real-time Bitcoin dominance percentage with historical context
✅ Sophisticated support and resistance level calculations
✅ Fibonacci-based technical levels and psychological levels
✅ Trend strength analysis (7-day and 30-day)
✅ Volatility measurements and range analysis
✅ Bitcoin price action and technical indicators
✅ Global cryptocurrency market data

ANALYSIS REQUIREMENTS:
- Explain current dominance level and its significance
- Analyze support and resistance levels - which are most critical?
- Identify key breakout/breakdown levels to watch
- Assess trend strength and momentum
- Discuss implications for altcoins (altseason vs BTC season)
- Provide specific entry/exit levels for dominance-based trading
- Consider market cycle phase and what dominance level suggests
- Explain correlation between BTC price movement and dominance changes
- Give actionable recommendations for portfolio allocation

```json
{json_data}
```

Provide comprehensive Bitcoin dominance technical analysis with specific levels and actionable trading insights."""

            # Get AI analysis using crypto_analyst prompt
            ai_generator = AIResponseGenerator(provider, model)
            system_prompt = PROMPTS.get('crypto_analyst', "You are a cryptocurrency analyst.")

            # Add language instruction if specified
            if language:
                if language == 'persian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Persian (Farsi). Use proper Persian financial terminology. Keep the friendly, conversational tone while using appropriate Persian expressions. Use Persian numbers and currency terms."
                elif language == 'arabic':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Arabic. Use proper Arabic financial terminology. Keep the friendly, conversational tone while using appropriate Arabic expressions."
                elif language == 'spanish':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Spanish. Use proper Spanish financial terminology. Keep the friendly, conversational tone while using appropriate Spanish expressions."
                elif language == 'french':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent French. Use proper French financial terminology. Keep the friendly, conversational tone while using appropriate French expressions."
                elif language == 'german':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent German. Use proper German financial terminology. Keep the friendly, conversational tone while using appropriate German expressions."
                elif language == 'italian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Italian. Use proper Italian financial terminology. Keep the friendly, conversational tone while using appropriate Italian expressions."
                elif language == 'portuguese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Portuguese. Use proper Portuguese financial terminology. Keep the friendly, conversational tone while using appropriate Portuguese expressions."
                elif language == 'russian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Russian. Use proper Russian financial terminology. Keep the friendly, conversational tone while using appropriate Russian expressions."
                elif language == 'chinese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Chinese (Simplified). Use proper Chinese financial terminology. Keep the friendly, conversational tone while using appropriate Chinese expressions."
                elif language == 'japanese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Japanese. Use proper Japanese financial terminology. Keep the friendly, conversational tone while using appropriate Japanese expressions."
                elif language == 'korean':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Korean. Use proper Korean financial terminology. Keep the friendly, conversational tone while using appropriate Korean expressions."
                else:  # english or default
                    system_prompt += "\n\nIMPORTANT: Respond in fluent English. Use proper English financial terminology. Keep the friendly, conversational tone."

            response_text = await ai_generator.generate_response(
                system_prompt=system_prompt,
                user_query=query,
                temperature=0.7,
                max_tokens=3000,
                use_search=False
            )

            # Format the response with better structure
            current_dominance_val = dominance_data.get('btc_dominance', 0)
            dominance_emoji = "👑" if current_dominance_val > 50 else "⚖️" if current_dominance_val > 40 else "📉"

            header = f"👑 **Bitcoin Dominance Technical Analysis** {dominance_emoji}\n"
            header += f"📊 **BTC.D: {current_dominance_val:.1f}%** | ETH.D: {eth_dominance:.1f}%\n"
            header += f"💰 Total Market: {format_large_number(dominance_data.get('total_market_cap', 0))}\n"

            # Add key levels to header if available
            if dominance_levels:
                header += f"🔴 **Resistance:** {dominance_levels.get('major_resistance', 'N/A')}% | "
                header += f"🟢 **Support:** {dominance_levels.get('major_support', 'N/A')}%\n"

                # Show trend
                trend_7d = dominance_levels.get('trend_strength_7d', 0)
                trend_emoji = "📈" if trend_7d > 0 else "📉" if trend_7d < 0 else "➡️"
                header += f"{trend_emoji} **7d Trend:** {trend_7d:+.2f}% | "
                header += f"📊 **Volatility:** {dominance_levels.get('volatility_14d', 'N/A')}%\n"

            header += f"⏰ *Analysis at {datetime.now().strftime('%H:%M')}*\n"
            header += "─" * 40 + "\n\n"

            final_response = header + response_text

            # Add footer with data quality info and key levels
            data_quality_info = dominance_analysis["data_quality"]
            footer = f"\n\n📊 **Technical Analysis Summary:**\n"

            if dominance_levels:
                footer += f"🎯 **Key Levels to Watch:**\n"
                footer += f"• Next Resistance: {dominance_levels.get('resistance_1', 'N/A')}% → {dominance_levels.get('resistance_2', 'N/A')}%\n"
                footer += f"• Next Support: {dominance_levels.get('support_1', 'N/A')}% → {dominance_levels.get('support_2', 'N/A')}%\n"

                if dominance_levels.get('psychological_levels'):
                    psych_levels = dominance_levels['psychological_levels'][:3]  # Show first 3
                    footer += f"• Psychological: {', '.join([f'{level}%' for level in psych_levels])}\n"

            footer += f"\n📈 **Data Quality:**\n"
            footer += f"• Dominance data: {'✅ Live' if data_quality_info['dominance_data_available'] else '❌ Limited'}\n"
            footer += f"• Technical levels: {'✅ Calculated' if data_quality_info['dominance_levels_available'] else '❌ Limited'}\n"
            footer += f"• BTC candles: {data_quality_info['btc_candles_count']}\n"
            footer += f"• Confidence: {data_quality_info['analysis_confidence'].title()}\n"
            footer += f"• Source: CoinGecko Global + Historical Data\n"
            footer += "⚠️ *Not financial advice. DYOR.*"

            final_response += footer

            # Send response in chunks if necessary
            chunks = [
                final_response[i : i + 4096] for i in range(0, len(final_response), 4096)
            ]

            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)

        except ValueError as e:
            await message.edit(f"❌ Error: {str(e)}")
        except requests.exceptions.RequestException as e:
            await message.edit(f"❌ Network error: {str(e)}")
        except Exception as e:
            await message.edit(f"❌ Unexpected error: {str(e)}")

    except Exception as e:
        await message.edit(f"❌ Command error: {str(e)}")

@Client.on_message(filters.me & filters.command("cryptokey", prefixes=[".", "!"]))
async def set_crypto_key(client: Client, message: Message):
    """Set CoinMarketCap API key"""
    try:
        cmd = message.command
        
        if len(cmd) < 2:
            await message.edit(
                "**Set CoinMarketCap API Key:**\n\n"
                "Usage: `.cryptokey <your_api_key>`\n\n"
                "Get your free API key from: https://coinmarketcap.com/api/"
            )
            return
        
        api_key = cmd[1]
        
        # Show instructions for setting up the API key
        await message.edit(
            "⚠️ **API Key Setup Instructions**\n\n"
            f"Add this line to your `.env` file:\n"
            f"`COINMARKETCAP_API_KEY={api_key}`\n\n"
            "Then restart the bot to use the new API key.\n\n"
            "**Security Note:** Keep your API key private and never share it!"
        )
        
    except Exception as e:
        await message.edit(f"❌ Error: {str(e)}")
