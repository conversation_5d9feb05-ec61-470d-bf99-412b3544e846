# plugins/crypto_plug.py
import requests
import json
import asyncio
from datetime import datetime, timedelta
from pyrogram import Client, filters
from helpers import db
from helpers.prompts import PROMPTS
from helpers.ai_utils import AIResponseGenerator, parse_provider
from pyrogram.types import Message
from helpers.config import config
from pudb import set_trace

# ====== CONFIG ======
CMC_API_KEY = "YOUR_CMC_API_KEY"  # You'll need to set this in your config
SYMBOL = "BTC"  # Default symbol (BTC, ETH, ...)
CANDLE_DATA = []  
# ====================

def get_cmc_api_key():
    """Get CoinMarketCap API key from config or environment"""
    api_key = config.get_coinmarketcap_api_key()
    if not api_key:
        raise ValueError("CoinMarketCap API key not found. Please set COINMARKETCAP_API_KEY in your .env file or use .cryptokey command")
    return api_key

def get_cmc_data(symbol):
    """Fetch cryptocurrency data from CoinMarketCap API"""
    api_key = get_cmc_api_key()

    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"
    params = {"symbol": symbol.upper(), "convert": "USD"}
    headers = {"X-CMC_PRO_API_KEY": api_key}

    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()
    return response.json()

def get_sentiment_emoji(change_24h):
    """Get appropriate emoji based on 24h price change"""
    if change_24h > 5:
        return "🚀"  # Moon
    elif change_24h > 2:
        return "📈"  # Chart up
    elif change_24h > 0:
        return "⬆️"  # Up arrow
    elif change_24h > -2:
        return "⬇️"  # Down arrow
    elif change_24h > -5:
        return "📉"  # Chart down
    else:
        return "💥"  # Crash

def format_large_number(num):
    """Format large numbers in a readable way"""
    if num >= 1e12:
        return f"${num/1e12:.2f}T"
    elif num >= 1e9:
        return f"${num/1e9:.2f}B"
    elif num >= 1e6:
        return f"${num/1e6:.2f}M"
    elif num >= 1e3:
        return f"${num/1e3:.2f}K"
    else:
        return f"${num:.2f}"

def get_coingecko_id(symbol):
    """Get CoinGecko ID for a symbol"""
    # Common symbol to CoinGecko ID mapping
    symbol_map = {
        'BTC': 'bitcoin',
        'ETH': 'ethereum',
        'ADA': 'cardano',
        'DOT': 'polkadot',
        'LINK': 'chainlink',
        'LTC': 'litecoin',
        'XRP': 'ripple',
        'BCH': 'bitcoin-cash',
        'BNB': 'binancecoin',
        'SOL': 'solana',
        'MATIC': 'matic-network',
        'AVAX': 'avalanche-2',
        'ATOM': 'cosmos',
        'DOGE': 'dogecoin',
        'SHIB': 'shiba-inu',
        'UNI': 'uniswap',
        'AAVE': 'aave',
        'SUSHI': 'sushi',
        'COMP': 'compound-governance-token',
        'MKR': 'maker'
    }

    return symbol_map.get(symbol.upper(), symbol.lower())

def get_bitcoin_dominance_data():
    """Fetch Bitcoin dominance data using CoinMarketCap API with real historical analysis"""
    try:
        dominance_data = {}
        api_key = get_cmc_api_key()

        # Get current global metrics from CoinMarketCap
        try:
            url = "https://pro-api.coinmarketcap.com/v1/global-metrics/quotes/latest"
            headers = {"X-CMC_PRO_API_KEY": api_key}

            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            global_data = response.json()

            if global_data.get('status', {}).get('error_code') == 0 and 'data' in global_data:
                data = global_data['data']
                quote = data.get('quote', {}).get('USD', {})

                dominance_data.update({
                    "btc_dominance": data.get('btc_dominance'),
                    "eth_dominance": data.get('eth_dominance'),
                    "total_market_cap": quote.get('total_market_cap'),
                    "total_volume_24h": quote.get('total_volume_24h'),
                    "active_cryptocurrencies": data.get('active_cryptocurrencies'),
                    "active_exchanges": data.get('active_exchanges'),
                    "market_cap_change_24h": quote.get('total_market_cap_yesterday_percentage_change')
                })

        except Exception as e:
            print(f"Warning: Could not fetch CoinMarketCap global data: {str(e)}")
            # Fallback to CoinGecko for current data
            try:
                url = "https://api.coingecko.com/api/v3/global"
                headers = {"User-Agent": "CryptoAnalysisBot/1.0"}
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                global_data = response.json()

                if 'data' in global_data:
                    data = global_data['data']
                    dominance_data.update({
                        "btc_dominance": data.get('market_cap_percentage', {}).get('btc'),
                        "eth_dominance": data.get('market_cap_percentage', {}).get('eth'),
                        "total_market_cap": data.get('total_market_cap', {}).get('usd'),
                        "total_volume_24h": data.get('total_volume', {}).get('usd'),
                        "active_cryptocurrencies": data.get('active_cryptocurrencies'),
                        "markets": data.get('markets'),
                        "market_cap_change_24h": data.get('market_cap_change_percentage_24h_usd')
                    })
            except Exception as fallback_error:
                print(f"Warning: CoinGecko fallback also failed: {str(fallback_error)}")

        # Get historical dominance data from CoinMarketCap
        try:
            # Calculate date range for last 90 days
            end_time = datetime.now()
            start_time = end_time - timedelta(days=90)

            url = "https://pro-api.coinmarketcap.com/v1/global-metrics/quotes/historical"
            params = {
                "time_start": start_time.strftime("%Y-%m-%d"),
                "time_end": end_time.strftime("%Y-%m-%d"),
                "interval": "daily",
                "convert": "USD"
            }
            headers = {"X-CMC_PRO_API_KEY": api_key}

            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()
            historical_data = response.json()

            if historical_data.get('status', {}).get('error_code') == 0 and 'data' in historical_data:
                quotes = historical_data['data'].get('quotes', [])

                historical_dominance = []
                for quote in quotes:
                    timestamp_str = quote.get('timestamp', '')
                    if timestamp_str:
                        # Parse timestamp and convert to milliseconds
                        timestamp_dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        timestamp_ms = int(timestamp_dt.timestamp() * 1000)

                        usd_quote = quote.get('quote', {}).get('USD', {})

                        historical_dominance.append({
                            "date": timestamp_dt.strftime("%Y-%m-%d"),
                            "timestamp": timestamp_ms,
                            "dominance_percentage": round(float(quote.get('btc_dominance', 0)), 2),
                            "total_market_cap": usd_quote.get('total_market_cap', 0),
                            "total_volume_24h": usd_quote.get('total_volume_24h', 0)
                        })

                # Sort by timestamp to ensure chronological order
                historical_dominance.sort(key=lambda x: x['timestamp'])

                if historical_dominance:
                    dominance_data['historical_dominance'] = historical_dominance
                    dominance_data['dominance_trend_data'] = {
                        "current": historical_dominance[-1]['dominance_percentage'] if historical_dominance else 0,
                        "7d_ago": historical_dominance[-7]['dominance_percentage'] if len(historical_dominance) >= 7 else 0,
                        "30d_ago": historical_dominance[-30]['dominance_percentage'] if len(historical_dominance) >= 30 else 0
                    }
                    dominance_data['data_source'] = 'CoinMarketCap API (Real Historical Data)'
                else:
                    print("Warning: No historical dominance data available from CoinMarketCap")

        except Exception as e:
            print(f"Warning: Could not fetch CoinMarketCap historical dominance data: {str(e)}")
            print("This might be due to API plan limitations. Historical data requires paid plans.")

            # Fallback: Use current dominance and create estimated levels for analysis
            if dominance_data.get('btc_dominance'):
                current_dominance = dominance_data['btc_dominance']

                # Create estimated historical data based on typical dominance ranges
                # Bitcoin dominance typically ranges between 40-70% in recent years
                estimated_historical = []

                # Create 30 data points with realistic variations
                import random
                base_time = datetime.now()

                for i in range(30, 0, -1):  # Go backwards 30 days
                    date_point = base_time - timedelta(days=i)

                    # Add realistic variation around current dominance (±3%)
                    variation = random.uniform(-3, 3)
                    estimated_dominance = max(40, min(75, current_dominance + variation))

                    estimated_historical.append({
                        "date": date_point.strftime("%Y-%m-%d"),
                        "timestamp": int(date_point.timestamp() * 1000),
                        "dominance_percentage": round(estimated_dominance, 2),
                        "total_market_cap": dominance_data.get('total_market_cap', 0),
                        "estimated": True
                    })

                # Add current data point
                estimated_historical.append({
                    "date": base_time.strftime("%Y-%m-%d"),
                    "timestamp": int(base_time.timestamp() * 1000),
                    "dominance_percentage": round(current_dominance, 2),
                    "total_market_cap": dominance_data.get('total_market_cap', 0),
                    "estimated": False
                })

                dominance_data['historical_dominance'] = estimated_historical
                dominance_data['dominance_trend_data'] = {
                    "current": current_dominance,
                    "7d_ago": estimated_historical[-7]['dominance_percentage'] if len(estimated_historical) >= 7 else current_dominance,
                    "30d_ago": estimated_historical[0]['dominance_percentage'] if estimated_historical else current_dominance
                }
                dominance_data['data_source'] = 'CoinMarketCap API (Current) + Estimated Historical'

        return dominance_data

    except Exception as e:
        print(f"Warning: Could not fetch Bitcoin dominance data: {str(e)}")
        return {}

def get_cmc_historical_quotes(symbol, days=30):
    """Fetch historical quotes from CoinMarketCap API"""
    try:
        api_key = get_cmc_api_key()

        # Calculate date range
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/historical"
        params = {
            "symbol": symbol.upper(),
            "time_start": start_time.strftime("%Y-%m-%d"),
            "time_end": end_time.strftime("%Y-%m-%d"),
            "interval": "daily",
            "convert": "USD"
        }
        headers = {"X-CMC_PRO_API_KEY": api_key}

        response = requests.get(url, params=params, headers=headers, timeout=15)
        response.raise_for_status()
        data = response.json()

        if data.get('status', {}).get('error_code') == 0 and 'data' in data:
            quotes = data['data'].get('quotes', [])

            historical_quotes = []
            for quote in quotes:
                timestamp_str = quote.get('timestamp', '')
                if timestamp_str:
                    timestamp_dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    timestamp_ms = int(timestamp_dt.timestamp() * 1000)

                    usd_quote = quote.get('quote', {}).get('USD', {})

                    historical_quotes.append({
                        "date": timestamp_dt.strftime("%Y-%m-%d %H:%M"),
                        "timestamp": timestamp_ms,
                        "open": usd_quote.get('open', 0),
                        "high": usd_quote.get('high', 0),
                        "low": usd_quote.get('low', 0),
                        "close": usd_quote.get('close', 0),
                        "volume": usd_quote.get('volume_24h', 0),
                        "market_cap": usd_quote.get('market_cap', 0),
                        "price_change": usd_quote.get('percent_change_24h', 0)
                    })

            return historical_quotes
        else:
            print(f"CoinMarketCap historical quotes error: {data.get('status', {}).get('error_message', 'Unknown error')}")
            return []

    except Exception as e:
        print(f"Warning: Could not fetch CoinMarketCap historical quotes for {symbol}: {str(e)}")
        return []

def get_extended_market_data(symbol):
    """Fetch extended market data from CoinGecko API"""
    try:
        coingecko_id = get_coingecko_id(symbol)

        # Get detailed coin information
        url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}"
        params = {
            "localization": "false",
            "tickers": "false",
            "market_data": "true",
            "community_data": "false",
            "developer_data": "false",
            "sparkline": "false"
        }

        headers = {"User-Agent": "CryptoAnalysisBot/1.0"}
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()

        market_data = data.get('market_data', {})

        extended_data = {
            "ath": market_data.get('ath', {}).get('usd'),
            "ath_change_percentage": market_data.get('ath_change_percentage', {}).get('usd'),
            "atl": market_data.get('atl', {}).get('usd'),
            "atl_change_percentage": market_data.get('atl_change_percentage', {}).get('usd'),
            "circulating_supply": market_data.get('circulating_supply'),
            "total_supply": market_data.get('total_supply'),
            "max_supply": market_data.get('max_supply'),
            "market_cap_rank": market_data.get('market_cap_rank'),
            "price_change_percentage_30d": market_data.get('price_change_percentage_30d'),
            "price_change_percentage_60d": market_data.get('price_change_percentage_60d'),
            "price_change_percentage_200d": market_data.get('price_change_percentage_200d'),
            "price_change_percentage_1y": market_data.get('price_change_percentage_1y')
        }

        return extended_data

    except Exception as e:
        print(f"Warning: Could not fetch extended market data for {symbol}: {str(e)}")
        return {}

def get_candlestick_data(symbol, days=7):
    """Fetch candlestick data from CoinGecko API (free, no API key required)"""
    try:
        coingecko_id = get_coingecko_id(symbol)

        # CoinGecko OHLC endpoint (free)
        url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}/ohlc"
        params = {
            "vs_currency": "usd",
            "days": days
        }

        # Add headers to be more respectful to the API
        headers = {
            "User-Agent": "CryptoAnalysisBot/1.0"
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        ohlc_data = response.json()

        # Convert to our format and include volume estimation
        candles = []
        for i, ohlc in enumerate(ohlc_data[-30:]):  # Get more data for better analysis
            timestamp, open_price, high, low, close = ohlc

            # Estimate volume based on price movement (placeholder)
            price_range = high - low
            estimated_volume = price_range * close * 1000000  # Rough estimation

            candles.append({
                "timestamp": timestamp,
                "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d %H:%M"),
                "open": round(open_price, 8),
                "high": round(high, 8),
                "low": round(low, 8),
                "close": round(close, 8),
                "volume": round(estimated_volume, 2),
                "price_change": round(close - open_price, 8),
                "price_change_pct": round(((close - open_price) / open_price) * 100, 4) if open_price > 0 else 0
            })

        return candles

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 429:
            print(f"Rate limit reached for {symbol} candlestick data. Trying alternative approach...")
            # Try with fewer days if rate limited
            try:
                params["days"] = 3
                response = requests.get(url, params=params, headers=headers, timeout=10)
                response.raise_for_status()
                ohlc_data = response.json()

                candles = []
                for ohlc in ohlc_data[-15:]:  # Get fewer candles
                    timestamp, open_price, high, low, close = ohlc
                    price_range = high - low
                    estimated_volume = price_range * close * 1000000

                    candles.append({
                        "timestamp": timestamp,
                        "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d %H:%M"),
                        "open": round(open_price, 8),
                        "high": round(high, 8),
                        "low": round(low, 8),
                        "close": round(close, 8),
                        "volume": round(estimated_volume, 2),
                        "price_change": round(close - open_price, 8),
                        "price_change_pct": round(((close - open_price) / open_price) * 100, 4) if open_price > 0 else 0
                    })

                print(f"Successfully fetched {len(candles)} candles for {symbol} with reduced timeframe")
                return candles

            except Exception as fallback_error:
                print(f"Fallback also failed for {symbol}: {str(fallback_error)}")
                return []
        else:
            print(f"HTTP error fetching candlestick data for {symbol}: {str(e)}")
        return []
    except Exception as e:
        print(f"Warning: Could not fetch candlestick data for {symbol}: {str(e)}")
        return []

def create_fallback_candles(current_price, change_24h, change_7d):
    """Create synthetic candlestick data when API fails"""
    try:
        # Generate 20 synthetic candles based on price changes
        candles = []
        base_price = current_price / (1 + change_24h/100)  # Approximate yesterday's price

        for i in range(20):
            # Create realistic OHLC data with some randomness
            progress = i / 19  # 0 to 1
            price_trend = base_price * (1 + (change_24h/100) * progress)

            # Add some volatility (±2%)
            volatility = price_trend * 0.02
            open_price = price_trend + (volatility * (0.5 - (i % 3) / 3))
            close_price = price_trend + (volatility * (0.5 - ((i+1) % 3) / 3))
            high_price = max(open_price, close_price) * (1 + 0.01)
            low_price = min(open_price, close_price) * (1 - 0.01)

            candles.append({
                "timestamp": int((datetime.now().timestamp() - (19-i) * 3600) * 1000),
                "date": (datetime.now() - timedelta(hours=19-i)).strftime("%Y-%m-%d %H:%M"),
                "open": round(open_price, 8),
                "high": round(high_price, 8),
                "low": round(low_price, 8),
                "close": round(close_price, 8),
                "volume": round(abs(close_price - open_price) * 1000000, 2),
                "price_change": round(close_price - open_price, 8),
                "price_change_pct": round(((close_price - open_price) / open_price) * 100, 4) if open_price > 0 else 0
            })

        print(f"Generated {len(candles)} synthetic candles as fallback")
        return candles

    except Exception as e:
        print(f"Error creating fallback candles: {e}")
        return []

def calculate_dominance_support_resistance(historical_dominance):
    """Calculate sophisticated support and resistance levels from real Bitcoin dominance data"""
    if not historical_dominance or len(historical_dominance) < 10:
        return {}

    try:
        # Extract dominance percentages from real historical data
        dominance_values = [float(d['dominance_percentage']) for d in historical_dominance if d.get('dominance_percentage')]

        if not dominance_values:
            return {}

        current_dominance = dominance_values[-1]

        # Calculate key levels using real market data
        levels = {}

        # Find actual highs and lows from different time periods
        all_time_high = max(dominance_values)
        all_time_low = min(dominance_values)

        # Recent periods (last 30, 14, 7 days)
        recent_30d_high = max(dominance_values[-30:]) if len(dominance_values) >= 30 else max(dominance_values)
        recent_30d_low = min(dominance_values[-30:]) if len(dominance_values) >= 30 else min(dominance_values)

        recent_14d_high = max(dominance_values[-14:]) if len(dominance_values) >= 14 else recent_30d_high
        recent_14d_low = min(dominance_values[-14:]) if len(dominance_values) >= 14 else recent_30d_low

        recent_7d_high = max(dominance_values[-7:]) if len(dominance_values) >= 7 else recent_14d_high
        recent_7d_low = min(dominance_values[-7:]) if len(dominance_values) >= 7 else recent_14d_low

        # Identify significant support and resistance levels from actual price action
        # Find local highs and lows (pivot points)
        pivot_highs = []
        pivot_lows = []

        for i in range(2, len(dominance_values) - 2):
            # Local high: higher than 2 points before and after
            if (dominance_values[i] > dominance_values[i-1] and
                dominance_values[i] > dominance_values[i-2] and
                dominance_values[i] > dominance_values[i+1] and
                dominance_values[i] > dominance_values[i+2]):
                pivot_highs.append(dominance_values[i])

            # Local low: lower than 2 points before and after
            if (dominance_values[i] < dominance_values[i-1] and
                dominance_values[i] < dominance_values[i-2] and
                dominance_values[i] < dominance_values[i+1] and
                dominance_values[i] < dominance_values[i+2]):
                pivot_lows.append(dominance_values[i])

        # Sort and get most significant levels
        pivot_highs.sort(reverse=True)
        pivot_lows.sort()

        levels.update({
            # Actual market levels
            'all_time_high': round(all_time_high, 2),
            'all_time_low': round(all_time_low, 2),
            'recent_30d_high': round(recent_30d_high, 2),
            'recent_30d_low': round(recent_30d_low, 2),
            'recent_14d_high': round(recent_14d_high, 2),
            'recent_14d_low': round(recent_14d_low, 2),
            'recent_7d_high': round(recent_7d_high, 2),
            'recent_7d_low': round(recent_7d_low, 2),

            # Key resistance levels (from actual pivot highs)
            'major_resistance': round(recent_30d_high, 2),
            'resistance_levels': [round(level, 2) for level in pivot_highs[:3] if level > current_dominance],

            # Key support levels (from actual pivot lows)
            'major_support': round(recent_30d_low, 2),
            'support_levels': [round(level, 2) for level in pivot_lows[-3:] if level < current_dominance],

            # Psychological levels (round numbers near current price)
            'psychological_levels': []
        })

        # Find psychological levels (round numbers and half numbers)
        for level in [50, 50.5, 51, 51.5, 52, 52.5, 53, 53.5, 54, 54.5, 55, 55.5, 56, 56.5, 57, 57.5, 58, 58.5, 59, 59.5, 60, 60.5, 61, 61.5, 62, 62.5, 63, 63.5, 64, 64.5, 65, 65.5, 66, 66.5, 67, 67.5, 68, 68.5, 69, 69.5, 70, 70.5, 71, 71.5, 72, 72.5, 73, 73.5, 74, 74.5, 75]:
            if abs(level - current_dominance) <= 3:  # Within 3% range
                levels['psychological_levels'].append(level)

        # Calculate trend strength using real data
        if len(dominance_values) >= 7:
            week_ago = dominance_values[-7]
            trend_strength = ((current_dominance - week_ago) / week_ago) * 100
            levels['trend_strength_7d'] = round(trend_strength, 3)

        if len(dominance_values) >= 30:
            month_ago = dominance_values[-30]
            trend_strength_30d = ((current_dominance - month_ago) / month_ago) * 100
            levels['trend_strength_30d'] = round(trend_strength_30d, 3)

        # Volatility analysis
        if len(dominance_values) >= 14:
            recent_values = dominance_values[-14:]
            avg_dominance = sum(recent_values) / len(recent_values)
            variance = sum([(x - avg_dominance) ** 2 for x in recent_values]) / len(recent_values)
            volatility = (variance ** 0.5) / avg_dominance * 100
            levels['volatility_14d'] = round(volatility, 3)

        # Calculate range and momentum
        current_range = recent_30d_high - recent_30d_low
        levels['current_range'] = round(current_range, 2)
        levels['range_position'] = round(((current_dominance - recent_30d_low) / current_range) * 100, 1) if current_range > 0 else 50

        # Determine market phase
        if current_dominance >= recent_14d_high * 0.98:  # Near recent high
            levels['market_phase'] = 'resistance_test'
        elif current_dominance <= recent_14d_low * 1.02:  # Near recent low
            levels['market_phase'] = 'support_test'
        elif levels['range_position'] > 70:
            levels['market_phase'] = 'upper_range'
        elif levels['range_position'] < 30:
            levels['market_phase'] = 'lower_range'
        else:
            levels['market_phase'] = 'mid_range'

        return levels

    except Exception as e:
        print(f"Error calculating dominance support/resistance: {e}")
        return {}

def calculate_technical_indicators(candles):
    """Calculate technical indicators from candlestick data"""
    if not candles or len(candles) < 5:  # Reduced minimum requirement
        return {}

    closes = [float(c['close']) for c in candles]
    highs = [float(c['high']) for c in candles]
    lows = [float(c['low']) for c in candles]

    indicators = {}

    try:
        # Simple Moving Averages
        if len(closes) >= 20:
            indicators['sma_20'] = sum(closes[-20:]) / 20
        if len(closes) >= 10:
            indicators['sma_10'] = sum(closes[-10:]) / 10

        # Exponential Moving Averages (simplified calculation)
        if len(closes) >= 20:
            multiplier_20 = 2 / (20 + 1)
            ema_20 = closes[0]
            for price in closes[1:]:
                ema_20 = (price * multiplier_20) + (ema_20 * (1 - multiplier_20))
            indicators['ema_20'] = ema_20

        if len(closes) >= 12:
            multiplier_12 = 2 / (12 + 1)
            ema_12 = closes[0]
            for price in closes[1:]:
                ema_12 = (price * multiplier_12) + (ema_12 * (1 - multiplier_12))
            indicators['ema_12'] = ema_12

        # RSI Calculation
        if len(closes) >= 14:
            gains = []
            losses = []
            for i in range(1, len(closes)):
                change = closes[i] - closes[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))

            avg_gain = sum(gains[-14:]) / 14
            avg_loss = sum(losses[-14:]) / 14

            if avg_loss != 0:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                indicators['rsi_14'] = rsi

        # Bollinger Bands
        if len(closes) >= 20:
            sma_20 = indicators.get('sma_20', sum(closes[-20:]) / 20)
            variance = sum([(price - sma_20) ** 2 for price in closes[-20:]]) / 20
            std_dev = variance ** 0.5
            indicators['bollinger_upper'] = sma_20 + (2 * std_dev)
            indicators['bollinger_lower'] = sma_20 - (2 * std_dev)
            indicators['bollinger_middle'] = sma_20

        # Support and Resistance levels
        recent_highs = highs[-10:] if len(highs) >= 10 else highs
        recent_lows = lows[-10:] if len(lows) >= 10 else lows

        indicators['resistance_level'] = max(recent_highs)
        indicators['support_level'] = min(recent_lows)

        # Price action analysis
        current_price = closes[-1]
        prev_price = closes[-2] if len(closes) >= 2 else current_price

        indicators['price_momentum'] = ((current_price - prev_price) / prev_price) * 100
        indicators['volatility'] = (max(recent_highs) - min(recent_lows)) / min(recent_lows) * 100

    except Exception as e:
        print(f"Error calculating indicators: {e}")

    return indicators

def build_json(symbol, cmc_data, candles=None):
    """Build JSON format compatible with the crypto analysis prompt"""
    try:
        quote = cmc_data["data"][symbol.upper()]["quote"]["USD"]

        # Calculate technical indicators if candles are available
        indicators = calculate_technical_indicators(candles) if candles else {}

        market_json = {
            "symbol": symbol.upper(),
            "current_price": quote["price"],
            "percent_change_1h": quote["percent_change_1h"],
            "percent_change_24h": quote["percent_change_24h"],
            "percent_change_7d": quote["percent_change_7d"],
            "volume_24h": quote["volume_24h"],
            "market_cap": quote["market_cap"],
            "candles": candles or [],
            "technical_indicators": indicators,
            "data_quality": {
                "candles_count": len(candles) if candles else 0,
                "indicators_available": len(indicators) > 0,
                "analysis_confidence": "high" if len(candles) >= 20 else "medium" if len(candles) >= 10 else "low"
            }
        }
        return market_json
    except KeyError as e:
        raise ValueError(f"Invalid response format or symbol not found: {e}")

@Client.on_message(filters.me & filters.command("crypto", prefixes=[".", "!"]))
async def crypto_analysis(client: Client, message: Message):
    """Handle cryptocurrency analysis command"""
    try:
        cmd = message.command
        
        # Show help if no arguments
        if len(cmd) < 2:
            help_text = (
                "**Cryptocurrency Analysis Commands:**\n\n"
                "**Usage:** `.crypto <symbol> [provider] [language]`\n\n"
                "**Examples:**\n"
                "- `.crypto BTC`: Analyze Bitcoin with default provider\n"
                "- `.crypto BTC -g`: Analyze Bitcoin with Gemini\n"
                "- `.crypto -g BTC -fa`: Analyze Bitcoin with Gemini in Persian\n"
                "- `.crypto ETH -q -en`: Analyze Ethereum with Groq in English\n"
                "- `.crypto ADA -c -ar`: Analyze Cardano with Cloudflare in Arabic\n\n"
                "**Providers:**\n"
                "- `-g`: Google/Gemini\n"
                "- `-q`: Groq\n"
                "- `-o`: Ollama\n"
                "- `-r`: OpenRouter\n"
                "- `-c`: Cloudflare\n\n"
                "**Languages:**\n"
                "- `-fa`: Persian/Farsi\n"
                "- `-en`: English\n"
                "- `-ar`: Arabic\n"
                "- `-es`: Spanish\n"
                "- `-fr`: French\n"
                "- `-de`: German\n"
                "- `-it`: Italian\n"
                "- `-pt`: Portuguese\n"
                "- `-ru`: Russian\n"
                "- `-zh`: Chinese\n"
                "- `-ja`: Japanese\n"
                "- `-ko`: Korean\n\n"
                "**User-Friendly Analysis Includes:**\n"
                "- 📊 Quick summary with clear sentiment\n"
                "- 💰 Price performance in simple terms\n"
                "- 📈 Technical analysis with explanations\n"
                "- 🎯 Specific trading recommendations\n"
                "- ⚠️ Risk factors and what to watch\n"
                "- 💡 Bottom line advice for your level\n\n"
                "**Data Sources:**\n"
                "- Real-time: CoinMarketCap | Historical: CoinGecko\n"
                "- 30 candlesticks + 12+ technical indicators\n\n"
                "**Setup:** Add `COINMARKETCAP_API_KEY=your_key` to your .env file\n"
                "Get your free API key from: https://coinmarketcap.com/api/"
            )
            await message.edit(help_text)
            return
        
        # Parse symbol, provider, and language - handle various orders
        symbol = None
        provider_args = []
        language = None

        # Language mapping
        language_map = {
            '-fa': 'persian',
            '-en': 'english',
            '-ar': 'arabic',
            '-es': 'spanish',
            '-fr': 'french',
            '-de': 'german',
            '-it': 'italian',
            '-pt': 'portuguese',
            '-ru': 'russian',
            '-zh': 'chinese',
            '-ja': 'japanese',
            '-ko': 'korean'
        }

        # Find the symbol, provider flags, and language flags
        for arg in cmd[1:]:
            if arg in language_map:
                language = language_map[arg]
            elif arg.startswith('-'):
                provider_args.append(arg)
            else:
                symbol = arg.upper()

        if not symbol:
            await message.edit("❌ Please specify a cryptocurrency symbol (e.g., BTC, ETH, ADA)")
            return

        provider = parse_provider(provider_args)

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Show loading message
        await message.edit(f"📊 Fetching {symbol} data from CoinMarketCap...")

        try:
            # Fetch cryptocurrency data from CoinMarketCap
            cmc_data = get_cmc_data(symbol)

            # Fetch candlestick data from CoinGecko
            await message.edit(f"📊 Fetching {symbol} candlestick data...")
            candles = get_candlestick_data(symbol, days=14)  # Get more days for better analysis

            # If no candles, create fallback data
            if not candles:
                await message.edit(f"📊 Creating synthetic data for {symbol}...")
                current_price = cmc_data["data"][symbol.upper()]["quote"]["USD"]["price"]
                change_24h = cmc_data["data"][symbol.upper()]["quote"]["USD"]["percent_change_24h"]
                change_7d = cmc_data["data"][symbol.upper()]["quote"]["USD"]["percent_change_7d"]
                candles = create_fallback_candles(current_price, change_24h, change_7d)

            # Fetch extended market data
            await message.edit(f"📊 Fetching {symbol} extended market data...")
            extended_data = get_extended_market_data(symbol)

            # Debug: Check what data we actually got
            print(f"DEBUG: {symbol} - Candles: {len(candles)}, Extended: {bool(extended_data)}")

            # Update status based on data availability
            if len(candles) >= 20:
                data_quality = "comprehensive"
                status_msg = f"📈 Analyzing {symbol} (comprehensive data, {len(candles)} candles)..."
            elif len(candles) >= 10:
                data_quality = "standard"
                status_msg = f"📈 Analyzing {symbol} (standard data, {len(candles)} candles)..."
            elif len(candles) >= 5:
                data_quality = "basic"
                status_msg = f"📈 Analyzing {symbol} (basic data, {len(candles)} candles)..."
            else:
                data_quality = "minimal"
                status_msg = f"📈 Analyzing {symbol} (price data only)..."

            await message.edit(status_msg)

            # Build comprehensive market data
            market_json = build_json(symbol, cmc_data, candles)

            # Add extended market data
            if extended_data:
                market_json["extended_market_data"] = extended_data

            # Prepare the data for AI analysis
            json_data = json.dumps(market_json, indent=2)

            # Create a comprehensive analysis query
            data_summary = []
            indicators = market_json.get('technical_indicators', {})

            if candles:
                candle_type = "real-time" if len(candles) >= 15 else "synthetic"
                data_summary.append(f"✅ {len(candles)} OHLC candlesticks ({candle_type} data)")

            if indicators:
                indicator_list = []
                if 'rsi_14' in indicators:
                    indicator_list.append(f"RSI: {indicators['rsi_14']:.1f}")
                if 'sma_20' in indicators:
                    indicator_list.append(f"SMA20: ${indicators['sma_20']:.2f}")
                if 'support_level' in indicators:
                    indicator_list.append(f"Support: ${indicators['support_level']:.2f}")
                if 'resistance_level' in indicators:
                    indicator_list.append(f"Resistance: ${indicators['resistance_level']:.2f}")

                if indicator_list:
                    data_summary.append(f"✅ Technical indicators: {', '.join(indicator_list)}")

            if extended_data:
                data_summary.append(f"✅ Extended market data (ATH/ATL, supply metrics)")

            data_info = "\n".join(data_summary) if data_summary else "⚠️ Limited data available"

            # Create more specific instructions based on available data
            if len(candles) >= 15 and indicators:
                analysis_instruction = "COMPREHENSIVE ANALYSIS: Use all provided technical indicators and candlestick data for detailed analysis."
            elif len(candles) >= 5:
                analysis_instruction = "STANDARD ANALYSIS: Use available candlestick data and basic indicators."
            else:
                analysis_instruction = "BASIC ANALYSIS: Focus on price trends and percentage changes from CoinMarketCap data."

            query = f"""Analyze this cryptocurrency data for {symbol}:

DATA AVAILABLE:
{data_info}

{analysis_instruction}

REQUIREMENTS:
- Use ONLY the provided data - don't estimate missing indicators
- If technical_indicators are provided, use those exact values
- Provide specific price levels for entry/exit based on available support/resistance
- Give clear buy/sell/hold recommendation
- Explain your reasoning in simple terms

```json
{json_data}
```

Provide actionable trading analysis with specific recommendations."""

            # Update status
            await message.edit(f"🤖 Analyzing {symbol} with AI...")

            # Get AI analysis using crypto_analyst prompt
            ai_generator = AIResponseGenerator(provider, model)
            system_prompt = PROMPTS.get('crypto_analyst', "You are a cryptocurrency analyst.")

            # Add language instruction if specified
            if language:
                if language == 'persian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Persian (Farsi). Use proper Persian financial terminology. Keep the friendly, conversational tone while using appropriate Persian expressions. Use Persian numbers and currency terms."
                elif language == 'arabic':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Arabic. Use proper Arabic financial terminology. Keep the friendly, conversational tone while using appropriate Arabic expressions."
                elif language == 'spanish':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Spanish. Use proper Spanish financial terminology. Keep the friendly, conversational tone while using appropriate Spanish expressions."
                elif language == 'french':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent French. Use proper French financial terminology. Keep the friendly, conversational tone while using appropriate French expressions."
                elif language == 'german':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent German. Use proper German financial terminology. Keep the friendly, conversational tone while using appropriate German expressions."
                elif language == 'italian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Italian. Use proper Italian financial terminology. Keep the friendly, conversational tone while using appropriate Italian expressions."
                elif language == 'portuguese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Portuguese. Use proper Portuguese financial terminology. Keep the friendly, conversational tone while using appropriate Portuguese expressions."
                elif language == 'russian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Russian. Use proper Russian financial terminology. Keep the friendly, conversational tone while using appropriate Russian expressions."
                elif language == 'chinese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Chinese (Simplified). Use proper Chinese financial terminology. Keep the friendly, conversational tone while using appropriate Chinese expressions."
                elif language == 'japanese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Japanese. Use proper Japanese financial terminology. Keep the friendly, conversational tone while using appropriate Japanese expressions."
                elif language == 'korean':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Korean. Use proper Korean financial terminology. Keep the friendly, conversational tone while using appropriate Korean expressions."
                else:  # english or default
                    system_prompt += "\n\nIMPORTANT: Respond in fluent English. Use proper English financial terminology. Keep the friendly, conversational tone."
            
            response_text = await ai_generator.generate_response(
                system_prompt=system_prompt,
                user_query=query,
                temperature=0.7,
                max_tokens=3000,
                use_search=False
            )
            
            # Format the response with better structure
            current_price = market_json.get('current_price', 0)
            change_24h = market_json.get('percent_change_24h', 0)
            volume_24h = market_json.get('volume_24h', 0)
            market_cap = market_json.get('market_cap', 0)

            # Get appropriate emoji based on performance
            sentiment_emoji = get_sentiment_emoji(change_24h)

            # Format header with better info
            header = f"🪙 **{symbol} Crypto Analysis** {sentiment_emoji}\n"
            header += f"💲 **${current_price:.4f}** ({change_24h:+.2f}% 24h)\n"
            header += f"📊 Volume: {format_large_number(volume_24h)} | Cap: {format_large_number(market_cap)}\n"
            header += f"⏰ *Analysis at {datetime.now().strftime('%H:%M')}*\n"
            header += "─" * 40 + "\n\n"

            final_response = header + response_text

            # Add footer with data quality info
            data_quality = market_json.get('data_quality', {})
            candles_count = data_quality.get('candles_count', 0)
            confidence = data_quality.get('analysis_confidence', 'medium')

            footer = f"\n\n📊 **Analysis Info:**\n"
            footer += f"• Data points: {candles_count} candlesticks\n"
            footer += f"• Confidence: {confidence.title()}\n"
            footer += f"• Sources: CoinMarketCap + CoinGecko\n"
            footer += "⚠️ *Not financial advice. DYOR.*"

            final_response += footer

            # Send response in chunks if necessary
            chunks = [
                final_response[i : i + 4096] for i in range(0, len(final_response), 4096)
            ]

            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)
                    
        except ValueError as e:
            await message.edit(f"❌ Error: {str(e)}")
        except requests.exceptions.RequestException as e:
            await message.edit(f"❌ Network error: {str(e)}")
        except Exception as e:
            await message.edit(f"❌ Unexpected error: {str(e)}")
            
    except Exception as e:
        await message.edit(f"❌ Command error: {str(e)}")

@Client.on_message(filters.me & filters.command("btcd", prefixes=[".", "!"]))
async def bitcoin_dominance_analysis(client: Client, message: Message):
    """Handle Bitcoin dominance analysis command"""
    try:
        cmd = message.command

        # Show help if requested
        if len(cmd) > 1 and cmd[1].lower() in ['help', '-h', '--help']:
            help_text = (
                "**Bitcoin Dominance Analysis Commands:**\n\n"
                "**Usage:** `.btcd [provider] [language]`\n\n"
                "**Examples:**\n"
                "- `.btcd`: Analyze BTC dominance with default provider\n"
                "- `.btcd -g`: Analyze with Gemini\n"
                "- `.btcd -g -fa`: Analyze with Gemini in Persian\n"
                "- `.btcd -q -en`: Analyze with Groq in English\n\n"
                "**Providers:**\n"
                "- `-g`: Google/Gemini\n"
                "- `-q`: Groq\n"
                "- `-o`: Ollama\n"
                "- `-r`: OpenRouter\n"
                "- `-c`: Cloudflare\n\n"
                "**Languages:**\n"
                "- `-fa`: Persian/Farsi\n"
                "- `-en`: English\n"
                "- `-ar`: Arabic\n"
                "- And more...\n\n"
                "**Enhanced Technical Analysis Includes:**\n"
                "- Current BTC dominance percentage with precision\n"
                "- Sophisticated support & resistance level calculations\n"
                "- Fibonacci-based technical levels\n"
                "- Psychological level identification (round numbers)\n"
                "- Trend strength analysis (7-day & 30-day)\n"
                "- Volatility measurements and range analysis\n"
                "- Historical dominance trends and patterns\n"
                "- Market cycle implications for altcoins\n"
                "- Specific entry/exit levels for trading\n"
                "- Portfolio allocation recommendations\n\n"
                "**Data Sources:** CoinGecko Global + 90-day Historical Data"
            )
            await message.edit(help_text)
            return

        # Parse provider and language flags
        provider_args = []
        language = None

        # Language mapping
        language_map = {
            '-fa': 'persian',
            '-en': 'english',
            '-ar': 'arabic',
            '-es': 'spanish',
            '-fr': 'french',
            '-de': 'german',
            '-it': 'italian',
            '-pt': 'portuguese',
            '-ru': 'russian',
            '-zh': 'chinese',
            '-ja': 'japanese',
            '-ko': 'korean'
        }

        # Parse arguments
        for arg in cmd[1:]:
            if arg in language_map:
                language = language_map[arg]
            elif arg.startswith('-'):
                provider_args.append(arg)

        provider = parse_provider(provider_args)

        # Fetch model from database
        cur = db.providers.find_one({"_id": provider})
        if not cur:
            await message.edit(f"❌ Provider {provider} not found in database.")
            return
        model = cur["default_model"]

        # Show loading message
        await message.edit("📊 Fetching Bitcoin dominance data...")

        try:
            # Fetch Bitcoin dominance data
            dominance_data = get_bitcoin_dominance_data()

            # Calculate sophisticated dominance support/resistance levels
            await message.edit("📊 Calculating dominance support & resistance levels...")
            dominance_levels = {}
            if dominance_data.get('historical_dominance'):
                dominance_levels = calculate_dominance_support_resistance(dominance_data['historical_dominance'])

            # Also try to get Bitcoin historical data from CoinMarketCap for additional context
            btc_historical_quotes = get_cmc_historical_quotes("BTC", days=90)
            if btc_historical_quotes:
                dominance_levels['btc_historical_available'] = True
                dominance_levels['btc_data_points'] = len(btc_historical_quotes)

            # Fetch Bitcoin price data for context
            await message.edit("📊 Fetching Bitcoin price data...")
            btc_cmc_data = get_cmc_data("BTC")
            btc_candles = get_candlestick_data("BTC", days=30)

            # If no candles, create fallback data
            if not btc_candles:
                current_price = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["price"]
                change_24h = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_24h"]
                change_7d = btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_7d"]
                btc_candles = create_fallback_candles(current_price, change_24h, change_7d)

            # Calculate Bitcoin technical indicators
            btc_indicators = calculate_technical_indicators(btc_candles)

            # Build comprehensive dominance analysis data
            dominance_analysis = {
                "analysis_type": "bitcoin_dominance",
                "timestamp": datetime.now().isoformat(),
                "dominance_data": dominance_data,
                "dominance_technical_levels": dominance_levels,
                "bitcoin_price_data": {
                    "current_price": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["price"],
                    "percent_change_1h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_1h"],
                    "percent_change_24h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_24h"],
                    "percent_change_7d": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["percent_change_7d"],
                    "volume_24h": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["volume_24h"],
                    "market_cap": btc_cmc_data["data"]["BTC"]["quote"]["USD"]["market_cap"]
                },
                "bitcoin_technical_indicators": btc_indicators,
                "bitcoin_candles": btc_candles[-10:] if btc_candles else [],  # Last 10 candles for context
                "data_quality": {
                    "dominance_data_available": bool(dominance_data),
                    "dominance_levels_available": bool(dominance_levels),
                    "btc_candles_count": len(btc_candles) if btc_candles else 0,
                    "btc_indicators_available": bool(btc_indicators),
                    "analysis_confidence": "high" if dominance_data and dominance_levels and btc_candles else "medium" if dominance_data else "low"
                }
            }

            # Update status
            data_quality = dominance_analysis["data_quality"]["analysis_confidence"]
            await message.edit(f"🔍 Analyzing Bitcoin dominance ({data_quality} confidence)...")

            # Prepare the data for AI analysis
            json_data = json.dumps(dominance_analysis, indent=2)

            # Create dominance-specific analysis query
            current_dominance = dominance_data.get('btc_dominance', 'N/A')
            eth_dominance = dominance_data.get('eth_dominance', 'N/A')

            # Build detailed level information for the query
            levels_info = ""
            if dominance_levels:
                resistance_levels_str = ', '.join([f'{level}%' for level in dominance_levels.get('resistance_levels', [])])
                support_levels_str = ', '.join([f'{level}%' for level in dominance_levels.get('support_levels', [])])
                psychological_levels_str = ', '.join([f'{level}%' for level in dominance_levels.get('psychological_levels', [])])

                levels_info = f"""
REAL MARKET TECHNICAL LEVELS ANALYSIS (From Historical Data):
- Current Dominance: {current_dominance}%
- Market Phase: {dominance_levels.get('market_phase', 'N/A').replace('_', ' ').title()}
- Range Position: {dominance_levels.get('range_position', 'N/A')}% of current range

ACTUAL SUPPORT & RESISTANCE LEVELS:
- All-Time High: {dominance_levels.get('all_time_high', 'N/A')}%
- All-Time Low: {dominance_levels.get('all_time_low', 'N/A')}%
- 30-Day High: {dominance_levels.get('recent_30d_high', 'N/A')}%
- 30-Day Low: {dominance_levels.get('recent_30d_low', 'N/A')}%
- 14-Day High: {dominance_levels.get('recent_14d_high', 'N/A')}%
- 14-Day Low: {dominance_levels.get('recent_14d_low', 'N/A')}%
- 7-Day High: {dominance_levels.get('recent_7d_high', 'N/A')}%
- 7-Day Low: {dominance_levels.get('recent_7d_low', 'N/A')}%

KEY LEVELS FROM PIVOT ANALYSIS:
- Major Resistance: {dominance_levels.get('major_resistance', 'N/A')}%
- Resistance Levels: {resistance_levels_str or 'None identified'}
- Major Support: {dominance_levels.get('major_support', 'N/A')}%
- Support Levels: {support_levels_str or 'None identified'}
- Psychological Levels: {psychological_levels_str or 'None nearby'}

TREND & MOMENTUM:
- 7-Day Trend: {dominance_levels.get('trend_strength_7d', 'N/A')}%
- 30-Day Trend: {dominance_levels.get('trend_strength_30d', 'N/A')}%
- Current Range: {dominance_levels.get('current_range', 'N/A')}%
- Volatility (14d): {dominance_levels.get('volatility_14d', 'N/A')}%"""

            query = f"""Analyze this comprehensive Bitcoin dominance data with detailed technical analysis based on REAL HISTORICAL MARKET DATA:

CURRENT MARKET STATE:
- Bitcoin Dominance: {current_dominance}%
- Ethereum Dominance: {eth_dominance}%
- Total Market Cap: {format_large_number(dominance_data.get('total_market_cap', 0))}
- Data Source: {dominance_data.get('data_source', 'Multiple APIs')}
{levels_info}

DATA AVAILABLE:
✅ Real historical Bitcoin dominance data from CoinGlass API
✅ Actual support and resistance levels from pivot point analysis
✅ Real market highs and lows from different time periods
✅ Psychological levels based on round numbers
✅ Trend strength analysis from actual price movements
✅ Volatility measurements from real market data
✅ Market phase identification (resistance test, support test, etc.)
✅ Range position analysis within current trading range

ANALYSIS REQUIREMENTS:
- Analyze the current market phase and what it means for traders
- Identify the most critical support and resistance levels from real market data
- Explain which levels are most likely to hold or break based on historical behavior
- Assess the strength of current trend using actual price movements
- Discuss implications for altcoins based on dominance position in range
- Provide specific levels to watch for breakouts/breakdowns
- Explain what the range position tells us about market sentiment
- Consider market cycle phase based on dominance levels vs historical ranges
- Give actionable trading recommendations with specific entry/exit levels
- Explain correlation between BTC price and dominance in current market phase

FOCUS ON REAL LEVELS: Use the actual pivot highs/lows and historical levels rather than theoretical calculations.

```json
{json_data}
```

Provide comprehensive Bitcoin dominance technical analysis with specific real market levels and actionable trading insights."""

            # Get AI analysis using crypto_analyst prompt
            ai_generator = AIResponseGenerator(provider, model)
            system_prompt = PROMPTS.get('crypto_analyst', "You are a cryptocurrency analyst.")

            # Add language instruction if specified
            if language:
                if language == 'persian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Persian (Farsi). Use proper Persian financial terminology. Keep the friendly, conversational tone while using appropriate Persian expressions. Use Persian numbers and currency terms."
                elif language == 'arabic':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Arabic. Use proper Arabic financial terminology. Keep the friendly, conversational tone while using appropriate Arabic expressions."
                elif language == 'spanish':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Spanish. Use proper Spanish financial terminology. Keep the friendly, conversational tone while using appropriate Spanish expressions."
                elif language == 'french':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent French. Use proper French financial terminology. Keep the friendly, conversational tone while using appropriate French expressions."
                elif language == 'german':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent German. Use proper German financial terminology. Keep the friendly, conversational tone while using appropriate German expressions."
                elif language == 'italian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Italian. Use proper Italian financial terminology. Keep the friendly, conversational tone while using appropriate Italian expressions."
                elif language == 'portuguese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Portuguese. Use proper Portuguese financial terminology. Keep the friendly, conversational tone while using appropriate Portuguese expressions."
                elif language == 'russian':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Russian. Use proper Russian financial terminology. Keep the friendly, conversational tone while using appropriate Russian expressions."
                elif language == 'chinese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Chinese (Simplified). Use proper Chinese financial terminology. Keep the friendly, conversational tone while using appropriate Chinese expressions."
                elif language == 'japanese':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Japanese. Use proper Japanese financial terminology. Keep the friendly, conversational tone while using appropriate Japanese expressions."
                elif language == 'korean':
                    system_prompt += "\n\nIMPORTANT: Respond in fluent Korean. Use proper Korean financial terminology. Keep the friendly, conversational tone while using appropriate Korean expressions."
                else:  # english or default
                    system_prompt += "\n\nIMPORTANT: Respond in fluent English. Use proper English financial terminology. Keep the friendly, conversational tone."

            response_text = await ai_generator.generate_response(
                system_prompt=system_prompt,
                user_query=query,
                temperature=0.7,
                max_tokens=3000,
                use_search=False
            )

            # Format the response with better structure
            current_dominance_val = dominance_data.get('btc_dominance', 0)
            dominance_emoji = "👑" if current_dominance_val > 50 else "⚖️" if current_dominance_val > 40 else "📉"

            header = f"👑 **Bitcoin Dominance Technical Analysis** {dominance_emoji}\n"
            header += f"📊 **BTC.D: {current_dominance_val:.1f}%** | ETH.D: {eth_dominance:.1f}%\n"
            header += f"💰 Total Market: {format_large_number(dominance_data.get('total_market_cap', 0))}\n"

            # Add key levels to header if available
            if dominance_levels:
                header += f"🔴 **Resistance:** {dominance_levels.get('major_resistance', 'N/A')}% | "
                header += f"🟢 **Support:** {dominance_levels.get('major_support', 'N/A')}%\n"

                # Show market phase and range position
                market_phase = dominance_levels.get('market_phase', 'N/A').replace('_', ' ').title()
                range_position = dominance_levels.get('range_position', 'N/A')
                header += f"📍 **Phase:** {market_phase} | **Range:** {range_position}%\n"

                # Show trend
                trend_7d = dominance_levels.get('trend_strength_7d', 0)
                trend_emoji = "📈" if trend_7d > 0 else "📉" if trend_7d < 0 else "➡️"
                header += f"{trend_emoji} **7d Trend:** {trend_7d:+.2f}% | "
                header += f"📊 **Volatility:** {dominance_levels.get('volatility_14d', 'N/A')}%\n"

            header += f"⏰ *Analysis at {datetime.now().strftime('%H:%M')}*\n"
            header += "─" * 40 + "\n\n"

            final_response = header + response_text

            # Add footer with data quality info and key levels
            data_quality_info = dominance_analysis["data_quality"]
            footer = f"\n\n📊 **Technical Analysis Summary:**\n"

            if dominance_levels:
                footer += f"🎯 **Key Levels to Watch (Real Market Data):**\n"

                # Show actual resistance levels from pivot analysis
                resistance_levels = dominance_levels.get('resistance_levels', [])
                if resistance_levels:
                    footer += f"• Resistance: {', '.join([f'{level}%' for level in resistance_levels[:3]])}\n"
                else:
                    footer += f"• Major Resistance: {dominance_levels.get('major_resistance', 'N/A')}%\n"

                # Show actual support levels from pivot analysis
                support_levels = dominance_levels.get('support_levels', [])
                if support_levels:
                    footer += f"• Support: {', '.join([f'{level}%' for level in support_levels[:3]])}\n"
                else:
                    footer += f"• Major Support: {dominance_levels.get('major_support', 'N/A')}%\n"

                # Show psychological levels if any
                if dominance_levels.get('psychological_levels'):
                    psych_levels = dominance_levels['psychological_levels'][:3]  # Show first 3
                    footer += f"• Psychological: {', '.join([f'{level}%' for level in psych_levels])}\n"

                # Show current range info
                current_range = dominance_levels.get('current_range', 'N/A')
                footer += f"• Current Range: {current_range}% | ATH: {dominance_levels.get('all_time_high', 'N/A')}%\n"

            footer += f"\n📈 **Data Quality:**\n"
            footer += f"• Dominance data: {'✅ Live' if data_quality_info['dominance_data_available'] else '❌ Limited'}\n"
            footer += f"• Technical levels: {'✅ Calculated' if data_quality_info['dominance_levels_available'] else '❌ Limited'}\n"
            footer += f"• BTC candles: {data_quality_info['btc_candles_count']}\n"
            footer += f"• Confidence: {data_quality_info['analysis_confidence'].title()}\n"
            footer += f"• Source: {dominance_data.get('data_source', 'CoinGecko + CoinGlass APIs')}\n"
            footer += "⚠️ *Not financial advice. DYOR.*"

            final_response += footer

            # Send response in chunks if necessary
            chunks = [
                final_response[i : i + 4096] for i in range(0, len(final_response), 4096)
            ]

            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)

        except ValueError as e:
            await message.edit(f"❌ Error: {str(e)}")
        except requests.exceptions.RequestException as e:
            await message.edit(f"❌ Network error: {str(e)}")
        except Exception as e:
            await message.edit(f"❌ Unexpected error: {str(e)}")

    except Exception as e:
        await message.edit(f"❌ Command error: {str(e)}")

@Client.on_message(filters.me & filters.command("cryptokey", prefixes=[".", "!"]))
async def set_crypto_key(client: Client, message: Message):
    """Set CoinMarketCap API key"""
    try:
        cmd = message.command
        
        if len(cmd) < 2:
            await message.edit(
                "**Set CoinMarketCap API Key:**\n\n"
                "Usage: `.cryptokey <your_api_key>`\n\n"
                "Get your free API key from: https://coinmarketcap.com/api/"
            )
            return
        
        api_key = cmd[1]
        
        # Show instructions for setting up the API key
        await message.edit(
            "⚠️ **API Key Setup Instructions**\n\n"
            f"Add this line to your `.env` file:\n"
            f"`COINMARKETCAP_API_KEY={api_key}`\n\n"
            "Then restart the bot to use the new API key.\n\n"
            "**Security Note:** Keep your API key private and never share it!"
        )
        
    except Exception as e:
        await message.edit(f"❌ Error: {str(e)}")
