from pyrogram import Client, filters, enums
from helpers import db, groq_cli, gemini_cli  # Assuming groq_cli is your Gemini API client
from helpers.prompts import PROMPTS
from google import genai
import httpx
import io
import os
import pathlib
from pudb import set_trace
from .translation_tools import ensure_language
# Supported MIME types and their extensions
SUPPORTED_TYPES = {
    'application/pdf': '.pdf',
    'application/x-javascript': '.js',
    'text/javascript': '.js',
    'application/x-python': '.py',
    'text/x-python': '.py',
    'text/plain': '.txt',
    'text/html': '.html',
    'text/css': '.css',
    'text/md': '.md',
    'text/csv': '.csv',
    'text/xml': '.xml',
    'text/rtf': '.rtf'
}

gemini_client = genai.Client(api_key="YOUR_GEMINI_API_KEY")  # Replace with your API key or use os.getenv()

@Client.on_message(filters.reply & filters.command('docprocess', prefixes=['.', '!']) & filters.me)
async def process_document(client, message):
    try:
        replied_msg = message.reply_to_message
        cmd = message.command
        # Check if an action is specified
        gemini_client = gemini_cli
        if len(cmd) < 2:
            await message.edit(
                "❌ Please specify an action. Usage: `.docprocess <action>`\n"
                "Available actions:\n"
                "- `upload`: Upload the document to Gemini File API and get a URI\n"
                "- `summarize`: Summarize the document\n"
                "- `extract`: Extract structured entities\n"
                "- `question <your_question>`: Answer a question about the document\n"
                "- `transcribe`: Transcribe to HTML\n"
                "- `ask <question>`: Ask a question about a previously uploaded document"
            )
            return

        action = cmd[1].lower()
        additional_input = ' '.join(cmd[2:]) if len(cmd) > 2 else None

        # Valid actions
        valid_actions = ['upload', 'summarize', 'extract', 'question', 'transcribe', 'ask']
        if action not in valid_actions and not action.startswith('question'):
            await message.edit(f"❌ Invalid action. Use one of: {', '.join(valid_actions)} or `question <your_question>`")
            return

        # Check for document or URL
        doc_data = None
        mime_type = None
        file_path = None
        file_id = None
        file_name = None
        if replied_msg.media == enums.MessageMediaType.DOCUMENT and replied_msg.document:
            file_id = replied_msg.document.file_id
            file_name = replied_msg.document.file_name
            mime_type = replied_msg.document.mime_type
            if mime_type not in SUPPORTED_TYPES:
                await message.edit(f"❌ Unsupported file type: `{mime_type}`. Supported types: {', '.join(SUPPORTED_TYPES.keys())}")
                return
        elif replied_msg.text or replied_msg.caption:
            doc_text = replied_msg.text or replied_msg.caption
            doc_url = None
            for url in doc_text.split():
                if any(url.endswith(ext) for ext in SUPPORTED_TYPES.values()):
                    doc_url = url
                    mime_type = next((mt for mt, ext in SUPPORTED_TYPES.items() if url.endswith(ext)), 'text/plain')
                    file_name = doc_url.split('/')[-1]
                    break
            if not doc_url:
                await message.edit("❌ No valid document URL found in the replied message.")
                return
        else:
            await message.edit("❌ Reply to a message with a supported document or URL.")
            return

        # Handle actions
        if action == 'upload':
            if replied_msg.document:
                await message.edit("📥 Downloading document...")
                file_path = await replied_msg.download()
                doc_data = pathlib.Path(file_path).read_bytes()
            elif doc_url:
                await message.edit("📥 Fetching document from URL...")
                doc_data = httpx.get(doc_url).content

            # Check if already uploaded
            existing_file = db.uploaded_files.find_one({
                'user_id': message.from_user.id,
                '$or': [
                    {'file_id': file_id} if file_id else {},
                    {'file_name': file_name}
                ]
            })
            if existing_file:
                await message.edit(f"✅ Document already uploaded!\nURI: `{existing_file['uri']}`\nUse `.docprocess ask <question>` to query it.")
            else:
                await message.edit("📤 Uploading document to Gemini File API...")
                sample_file = gemini_client.files.upload(
                    file=io.BytesIO(doc_data),
                    config=dict(mime_type=mime_type)
                )
                uri = sample_file.uri
                db.uploaded_files.insert_one({
                    'user_id': message.from_user.id,
                    'uri': uri,
                    'file_id': file_id if file_id else None,
                    'file_name': file_name,
                    'upload_time': message.date
                })
                await message.edit(f"✅ Document uploaded successfully!\nURI: `{uri}`\nUse `.docprocess ask <question>` to query it.")

        elif action == 'ask':
            if not additional_input:
                await message.edit("❌ Please provide a question with `ask <question>`.")
                return
            # Check database for existing upload
            uploaded_file = db.uploaded_files.find_one({
                'user_id': message.from_user.id,
                '$or': [
                    {'file_id': file_id} if file_id else {},
                    {'file_name': file_name}
                ]
            })
            if not uploaded_file:
                await message.edit("❌ No previously uploaded document found for this file. Use `.docprocess upload` first.")
                return

            uri = uploaded_file['uri']
            await message.edit(f"🧠 Asking question about document at `{uri}`...")
            # Fetch the file metadata to get the sample_file object (not re-downloading)
            sample_file = gemini_client.files.get(name=uri)
            response = gemini_client.models.generate_content(
                model="gemini-1.5-flash",
                contents=[
                    sample_file,  # Use the file object, not just the URI
                    f"Answer this question about the document: {additional_input}"
                ]
            )
            response_text = response.text

            # Ensure response is in user's preferred language
            response_text = await ensure_language(response_text, message.from_user.id, db)

            chunks = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)

        else:
            # Direct processing actions (summarize, extract, question, transcribe)
            if replied_msg.document:
                await message.edit("📥 Downloading document...")
                file_path = await replied_msg.download()
                doc_data = pathlib.Path(file_path).read_bytes()
            elif doc_url:
                await message.edit("📥 Fetching document from URL...")
                doc_data = httpx.get(doc_url).content

            if action == 'summarize':
                prompt = "Summarize this document."
            elif action == 'extract':
                prompt = "Extract structured entities (e.g., names, dates, numbers) from this document in JSON format."
            elif action.startswith('question'):
                if not additional_input:
                    await message.edit("❌ Please provide a question with `question <your_question>`.")
                    return
                prompt = f"Answer this question about the document: {additional_input}"
            elif action == 'transcribe':
                prompt = "Transcribe this document to HTML, preserving layout and formatting where possible."

            await message.edit(f"🧠 Processing document with Gemini ({action})...")
            response = gemini_client.models.generate_content(
                model="gemini-1.5-flash",
                contents=[
                    genai.types.Part.from_bytes(
                        data=doc_data,
                        mime_type=mime_type
                    ),
                    prompt
                ]
            )
            response_text = response.text
            chunks = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
            for i, chunk in enumerate(chunks):
                if i == 0:
                    await message.edit(chunk)
                else:
                    await message.reply(chunk)

        # Clean up downloaded file if it exists
        if file_path and os.path.exists(file_path):
            os.remove(file_path)

    except Exception as e:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
        await message.edit(f"⚠️ Error processing document: {str(e)}")

# @Client.on_message(filters.reply & filters.command('docprocess', prefixes=['.', '!']) & filters.me)
# async def process_document(client, message):
#     try:
#         replied_msg = message.reply_to_message
#         cmd = message.command

#         # Check if an action is specified
#         if len(cmd) < 2:
#             await message.edit(
#                 "❌ Please specify an action. Usage: `.docprocess <action>`\n"
#                 "Available actions:\n"
#                 "- `summarize`: Summarize the document\n"
#                 "- `extract`: Extract structured entities\n"
#                 "- `question <your_question>`: Answer a question about the document\n"
#                 "- `transcribe`: Transcribe to HTML"
#             )
#             return

#         action = cmd[1].lower()
#         additional_input = ' '.join(cmd[2:]) if len(cmd) > 2 else None

#         # Valid actions
#         valid_actions = ['summarize', 'extract', 'question', 'transcribe']
#         if action not in valid_actions and not action.startswith('question'):
#             await message.edit(f"❌ Invalid action. Use one of: {', '.join(valid_actions)} or `question <your_question>`")
#             return

#         # Initialize Gemini client
#         gemini_client = gemini_cli

#         # Check for document or URL
#         doc_data = None
#         mime_type = None

#         set_trace()
#         if replied_msg.media == enums.MessageMediaType.DOCUMENT and replied_msg.document:
#             # Handle uploaded document
#             if replied_msg.document.mime_type not in SUPPORTED_TYPES:
#                 await message.edit(f"❌ Unsupported file type: `{replied_msg.document.mime_type}`. Supported types: {', '.join(SUPPORTED_TYPES.keys())}")
#                 return

#             mime_type = replied_msg.document.mime_type
#             await message.edit("📥 Downloading document...")
#             file_path = await replied_msg.download()

#             try:
#                 doc_data = pathlib.Path(file_path).read_bytes()
#             finally:
#                 # Clean up the downloaded file
#                 if os.path.exists(file_path):
#                     os.remove(file_path)

#         elif replied_msg.text or replied_msg.caption:
#             # Check for URL in text or caption
#             doc_text = replied_msg.text or replied_msg.caption
#             doc_url = None
#             for url in doc_text.split():
#                 if any(url.endswith(ext) for ext in SUPPORTED_TYPES.values()):
#                     doc_url = url
#                     mime_type = next((mt for mt, ext in SUPPORTED_TYPES.items() if url.endswith(ext)), 'text/plain')
#                     break

#             if doc_url:
#                 await message.edit("📥 Fetching document from URL...")
#                 try:
#                     doc_data = httpx.get(doc_url).content
#                 except Exception as e:
#                     await message.edit(f"⚠️ Error fetching document from URL: {str(e)}")
#                     return
#             else:
#                 await message.edit("❌ No valid document URL found in the replied message.")
#                 return
#         else:
#             await message.edit("❌ Reply to a message with a supported document or URL.")
#             return

#         if not doc_data:
#             await message.edit("❌ Failed to retrieve document data.")
#             return

#         # Determine prompt based on action
#         if action == 'summarize':
#             prompt = "Summarize this document."
#         elif action == 'extract':
#             prompt = "Extract structured entities (e.g., names, dates, numbers) from this document in JSON format."
#         elif action.startswith('question'):
#             if not additional_input:
#                 await message.edit("❌ Please provide a question with `question <your_question>`.")
#                 return
#             prompt = f"Answer this question about the document: {additional_input}"
#         elif action == 'transcribe':
#             prompt = "Transcribe this document to HTML, preserving layout and formatting where possible."

#         # Process with Gemini API
#         await message.edit(f"🧠 Processing document with Gemini ({action})...")

#         response = gemini_client.models.generate_content(
#             model="gemini-1.5-flash",
#             contents=[
#                 genai.types.Part.from_bytes(
#                     data=doc_data,
#                     mime_type=mime_type
#                 ),
#                 prompt
#             ]
#         )

#         # Handle response
#         response_text = response.text

#         # Split response if too long for Telegram (4096 chars)
#         chunks = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
#         for i, chunk in enumerate(chunks):
#             if i == 0:
#                 await message.edit(chunk)
#             else:
#                 await message.reply(chunk)

#     except Exception as e:
#         await message.edit(f"⚠️ Error processing document: {str(e)}")


# @Client.on_message(filters.reply & filters.command('docprocess', prefixes=['.', '!']) & filters.me)
# async def process_document(client, message):
#     try:
#         replied_msg = message.reply_to_message
#         cmd = message.command
#         set_trace()

#         # Check if an action is specified
#         if len(cmd) < 2:
#             await message.edit(
#                 "❌ Please specify an action. Usage: `.docprocess <action>`\n"
#                 "Available actions:\n"
#                 "- `upload`: Upload the document to Gemini File API and get a URI\n"
#                 "- `summarize`: Summarize the document\n"
#                 "- `extract`: Extract structured entities\n"
#                 "- `question <your_question>`: Answer a question about the document\n"
#                 "- `transcribe`: Transcribe to HTML\n"
#                 "- `ask <question>`: Ask a question about a previously uploaded document (requires URI)"
#             )
#             return

#         action = cmd[1].lower()
#         additional_input = ' '.join(cmd[2:]) if len(cmd) > 2 else None

#         gemini_client = gemini_cli
#         # Valid actions
#         valid_actions = ['upload', 'summarize', 'extract', 'question', 'transcribe', 'ask']
#         if action not in valid_actions and not action.startswith('question'):
#             await message.edit(f"❌ Invalid action. Use one of: {', '.join(valid_actions)} or `question <your_question>`")
#             return

#         # Check for document or URL
#         doc_data = None
#         mime_type = None
#         file_path = None

#         if replied_msg.media == enums.MessageMediaType.DOCUMENT and replied_msg.document:
#             # Handle uploaded document
#             if replied_msg.document.mime_type not in SUPPORTED_TYPES:
#                 await message.edit(f"❌ Unsupported file type: `{replied_msg.document.mime_type}`. Supported types: {', '.join(SUPPORTED_TYPES.keys())}")
#                 return

#             mime_type = replied_msg.document.mime_type
#             await message.edit("📥 Downloading document...")
#             file_path = await replied_msg.download()
#             doc_data = pathlib.Path(file_path).read_bytes()

#         elif replied_msg.text or replied_msg.caption:
#             # Check for URL in text or caption
#             doc_text = replied_msg.text or replied_msg.caption
#             doc_url = None
#             for url in doc_text.split():
#                 if any(url.endswith(ext) for ext in SUPPORTED_TYPES.values()):
#                     doc_url = url
#                     mime_type = next((mt for mt, ext in SUPPORTED_TYPES.items() if url.endswith(ext)), 'text/plain')
#                     break

#             if doc_url:
#                 await message.edit("📥 Fetching document from URL...")
#                 try:
#                     doc_data = httpx.get(doc_url).content
#                 except Exception as e:
#                     await message.edit(f"⚠️ Error fetching document from URL: {str(e)}")
#                     return
#             else:
#                 await message.edit("❌ No valid document URL found in the replied message.")
#                 return
#         else:
#             await message.edit("❌ Reply to a message with a supported document or URL.")
#             return

#         if not doc_data:
#             await message.edit("❌ Failed to retrieve document data.")
#             return

#         # Handle actions
#         if action == 'upload':
#             await message.edit("📤 Uploading document to Gemini File API...")
#             sample_file = gemini_client.files.upload(
#                 file=io.BytesIO(doc_data),
#                 config=dict(mime_type=mime_type)
#             )
#             uri = sample_file.uri
#             # Store URI in database for later use (optional)
#             db.uploaded_files.insert_one({
#                 'user_id': message.from_user.id,
#                 'uri': uri,
#                 'file_name': replied_msg.document.file_name if replied_msg.document else doc_url.split('/')[-1],
#                 'upload_time': message.date
#             })
#             await message.edit(f"✅ Document uploaded successfully!\nURI: `{uri}`\nUse `.docprocess ask <question>` with this URI to query it.")

#         elif action == 'ask':
#             if not additional_input:
#                 await message.edit("❌ Please provide a question with `ask <question>`.")
#                 return
#             # Look for the most recent uploaded file URI for this user
#             uploaded_file = db.uploaded_files.find_one(
#                 {'user_id': message.from_user.id},
#                 sort=[('upload_time', -1)]  # Get the latest
#             )
#             if not uploaded_file:
#                 await message.edit("❌ No previously uploaded document found. Use `.docprocess upload` first.")
#                 return
#             uri = uploaded_file['uri']
#             await message.edit(f"🧠 Asking question about document at `{uri}`...")
#             response = gemini_client.models.generate_content(
#                 model="gemini-1.5-flash",
#                 contents=[uri, f"Answer this question about the document: {additional_input}"]
#             )
#             response_text = response.text
#             chunks = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
#             for i, chunk in enumerate(chunks):
#                 if i == 0:
#                     await message.edit(chunk)
#                 else:
#                     await message.reply(chunk)

#         else:
#             # Direct processing actions (summarize, extract, question, transcribe)
#             if action == 'summarize':
#                 prompt = "Summarize this document."
#             elif action == 'extract':
#                 prompt = "Extract structured entities (e.g., names, dates, numbers) from this document in JSON format."
#             elif action.startswith('question'):
#                 if not additional_input:
#                     await message.edit("❌ Please provide a question with `question <your_question>`.")
#                     return
#                 prompt = f"Answer this question about the document: {additional_input}"
#             elif action == 'transcribe':
#                 prompt = "Transcribe this document to HTML, preserving layout and formatting where possible."

#             await message.edit(f"🧠 Processing document with Gemini ({action})...")
#             response = gemini_client.models.generate_content(
#                 model="gemini-1.5-flash",
#                 contents=[
#                     genai.types.Part.from_bytes(
#                         data=doc_data,
#                         mime_type=mime_type
#                     ),
#                     prompt
#                 ]
#             )
#             response_text = response.text
#             chunks = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
#             for i, chunk in enumerate(chunks):
#                 if i == 0:
#                     await message.edit(chunk)
#                 else:
#                     await message.reply(chunk)

#         # Clean up downloaded file if it exists
#         if file_path and os.path.exists(file_path):
#             os.remove(file_path)

#     except Exception as e:
#         if file_path and os.path.exists(file_path):
#             os.remove(file_path)
#         await message.edit(f"⚠️ Error processing document: {str(e)}")