from pyrogram import Client, filters
from helpers import db

# Voice lists from tts_plug.py
GROQ_ENGLISH_VOICES = [
    'Arista-PlayAI', 'Atlas-PlayAI', 'Basil-PlayAI', 'Briggs-PlayAI',
    'Calum-PlayAI', 'Celeste-PlayAI', 'Cheyenne-PlayAI', 'Chip-PlayAI',
    'Cillian-PlayAI', 'Deedee-PlayAI', 'Fritz-PlayAI', 'Gail-PlayAI',
    'Indigo-PlayAI', 'Mamaw-PlayAI', 'Mason-PlayAI', '<PERSON><PERSON>l-PlayAI',
    'Mitch-PlayAI', 'Quinn-PlayAI', 'Thunder-PlayAI'
]

GROQ_ARABIC_VOICES = ['Ahmad-PlayAI', 'Amira-PlayAI', 'Khalid-PlayAI', 'Nasser-PlayAI']

GEMINI_VOICES = [
    "<PERSON>ephyr", "<PERSON>uck", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fen<PERSON>r", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>oe<PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>no<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>lam", "Schedar", "Gacrux", "Pulcherrima", "Achird", "Zubenelgenubi",
    "Vindemiatrix", "Sadachbia", "Sadaltager", "Sulafat"
]

# Voice descriptions for popular Gemini voices
GEMINI_VOICE_DESCRIPTIONS = {
    "Kore": "Firm and confident",
    "Puck": "Upbeat and energetic",
    "Charon": "Informative and clear",
    "Zephyr": "Bright and cheerful",
    "Fenrir": "Excitable and dynamic",
    "Leda": "Youthful and vibrant",
    "Enceladus": "Breathy and soft",
    "Achernar": "Soft and gentle",
    "Sulafat": "Warm and friendly",
    "Aoede": "Breezy and light",
    "Despina": "Elegant and refined",
    "Orus": "Deep and resonant"
}

def get_user_voice_prefs(user_id):
    """Get user's voice preferences from database"""
    prefs = db.tts_voice_prefs.find_one({'user_id': user_id})
    if not prefs:
        # Return default voices
        return {
            'gemini': 'Kore',
            'groq': 'Fritz-PlayAI',
            'cloudflare': None  # Cloudflare doesn't have voice selection
        }
    return prefs.get('voices', {
        'gemini': 'Kore',
        'groq': 'Fritz-PlayAI',
        'cloudflare': None
    })

def set_user_voice_pref(user_id, provider, voice):
    """Set user's voice preference for a provider"""
    db.tts_voice_prefs.update_one(
        {'user_id': user_id},
        {'$set': {f'voices.{provider}': voice}},
        upsert=True
    )

@Client.on_message(filters.command(['ttsvoice', 'setvoice'], prefixes=['.', '!']) & filters.me)
async def set_tts_voice(client, message):
    """Set default TTS voice for a provider"""
    try:
        cmd = message.command
        if len(cmd) < 3:
            await message.edit(
                "❌ **Usage:** `.ttsvoice <provider> <voice_name>`\n\n"
                "**Examples:**\n"
                "• `.ttsvoice gemini Despina`\n"
                "• `.ttsvoice groq Celeste-PlayAI`\n\n"
                "**Providers:** `gemini`, `groq`\n"
                "**Note:** Cloudflare doesn't support voice selection\n\n"
                "Use `.ttsvoices` to see all available voices."
            )
            return

        provider = cmd[1].lower()
        voice_name = cmd[2]

        # Validate provider
        if provider not in ['gemini', 'groq']:
            await message.edit(
                "❌ **Invalid provider!**\n\n"
                "**Supported providers:** `gemini`, `groq`\n"
                "**Note:** Cloudflare doesn't support voice selection"
            )
            return

        # Validate voice for provider
        if provider == 'gemini':
            if voice_name not in GEMINI_VOICES:
                await message.edit(
                    f"❌ **Invalid Gemini voice:** `{voice_name}`\n\n"
                    "Use `.ttsvoices gemini` to see all available Gemini voices."
                )
                return
        elif provider == 'groq':
            all_groq_voices = GROQ_ENGLISH_VOICES + GROQ_ARABIC_VOICES
            if voice_name not in all_groq_voices:
                await message.edit(
                    f"❌ **Invalid Groq voice:** `{voice_name}`\n\n"
                    "Use `.ttsvoices groq` to see all available Groq voices."
                )
                return

        # Set the voice preference
        user_id = message.from_user.id
        set_user_voice_pref(user_id, provider, voice_name)

        # Get voice description if available
        description = ""
        if provider == 'gemini' and voice_name in GEMINI_VOICE_DESCRIPTIONS:
            description = f" ({GEMINI_VOICE_DESCRIPTIONS[voice_name]})"

        await message.edit(
            f"✅ **Default {provider.capitalize()} voice set!**\n\n"
            f"🎭 **Voice:** `{voice_name}`{description}\n"
            f"🔧 **Provider:** `{provider}`\n\n"
            f"Now all `.tts -{provider}` commands will use this voice by default."
        )

    except Exception as e:
        await message.edit(f"⚠️ **Error setting voice:** `{str(e)}`")

@Client.on_message(filters.command(['ttsvoices', 'listvoices'], prefixes=['.', '!']) & filters.me)
async def list_tts_voices(client, message):
    """List available TTS voices for providers"""
    try:
        cmd = message.command
        provider = cmd[1].lower() if len(cmd) > 1 else None

        if provider and provider not in ['gemini', 'groq', 'all']:
            await message.edit(
                "❌ **Invalid provider!**\n\n"
                "**Usage:** `.ttsvoices [provider]`\n"
                "**Providers:** `gemini`, `groq`, `all`\n"
                "**Example:** `.ttsvoices gemini`"
            )
            return

        # Get user's current preferences
        user_id = message.from_user.id
        current_voices = get_user_voice_prefs(user_id)

        if provider == 'gemini' or provider is None:
            # Show Gemini voices
            gemini_text = "🎭 **Gemini Voices (30 available):**\n\n"
            
            # Show popular voices first with descriptions
            popular_voices = ["Kore", "Puck", "Charon", "Zephyr", "Fenrir", "Enceladus", "Achernar", "Sulafat"]
            gemini_text += "**🌟 Popular Voices:**\n"
            for voice in popular_voices:
                if voice in GEMINI_VOICES:
                    current_marker = " ⭐" if current_voices.get('gemini') == voice else ""
                    desc = GEMINI_VOICE_DESCRIPTIONS.get(voice, "")
                    gemini_text += f"• `{voice}` - {desc}{current_marker}\n"
            
            # Show remaining voices
            remaining_voices = [v for v in GEMINI_VOICES if v not in popular_voices]
            if remaining_voices:
                gemini_text += f"\n**📋 All Voices ({len(GEMINI_VOICES)} total):**\n"
                # Group voices in rows of 3
                for i in range(0, len(GEMINI_VOICES), 3):
                    row_voices = GEMINI_VOICES[i:i+3]
                    row_text = ""
                    for voice in row_voices:
                        current_marker = " ⭐" if current_voices.get('gemini') == voice else ""
                        row_text += f"`{voice}`{current_marker}  "
                    gemini_text += row_text.strip() + "\n"
            
            gemini_text += f"\n**Current default:** `{current_voices.get('gemini', 'Kore')}`\n"
            gemini_text += "**Set voice:** `.ttsvoice gemini <voice_name>`"

            if provider == 'gemini':
                await message.edit(gemini_text)
                return

        if provider == 'groq' or provider is None:
            # Show Groq voices
            groq_text = "🎤 **Groq Voices (23 available):**\n\n"
            
            groq_text += "**🇺🇸 English Voices:**\n"
            # Group English voices in rows of 2
            for i in range(0, len(GROQ_ENGLISH_VOICES), 2):
                row_voices = GROQ_ENGLISH_VOICES[i:i+2]
                row_text = ""
                for voice in row_voices:
                    current_marker = " ⭐" if current_voices.get('groq') == voice else ""
                    row_text += f"`{voice}`{current_marker}  "
                groq_text += row_text.strip() + "\n"
            
            groq_text += "\n**🇸🇦 Arabic Voices:**\n"
            for voice in GROQ_ARABIC_VOICES:
                current_marker = " ⭐" if current_voices.get('groq') == voice else ""
                groq_text += f"• `{voice}`{current_marker}\n"
            
            groq_text += f"\n**Current default:** `{current_voices.get('groq', 'Fritz-PlayAI')}`\n"
            groq_text += "**Set voice:** `.ttsvoice groq <voice_name>`"

            if provider == 'groq':
                await message.edit(groq_text)
                return

        # Show all providers (default behavior)
        if provider is None or provider == 'all':
            summary_text = "🎵 **TTS Voice Management**\n\n"
            
            summary_text += "**📊 Current Defaults:**\n"
            summary_text += f"• **Gemini:** `{current_voices.get('gemini', 'Kore')}`\n"
            summary_text += f"• **Groq:** `{current_voices.get('groq', 'Fritz-PlayAI')}`\n"
            summary_text += f"• **Cloudflare:** No voice selection\n\n"
            
            summary_text += "**🔧 Commands:**\n"
            summary_text += "• `.ttsvoices gemini` - List Gemini voices\n"
            summary_text += "• `.ttsvoices groq` - List Groq voices\n"
            summary_text += "• `.ttsvoice <provider> <voice>` - Set default voice\n\n"
            
            summary_text += "**📈 Voice Counts:**\n"
            summary_text += f"• **Gemini:** {len(GEMINI_VOICES)} voices\n"
            summary_text += f"• **Groq:** {len(GROQ_ENGLISH_VOICES + GROQ_ARABIC_VOICES)} voices\n"
            summary_text += f"• **Cloudflare:** 1 voice (no selection)\n\n"
            
            summary_text += "**💡 Examples:**\n"
            summary_text += "• `.ttsvoice gemini Despina`\n"
            summary_text += "• `.ttsvoice groq Celeste-PlayAI`"

            await message.edit(summary_text)

    except Exception as e:
        await message.edit(f"⚠️ **Error listing voices:** `{str(e)}`")

@Client.on_message(filters.command(['ttsreset', 'resetvoice'], prefixes=['.', '!']) & filters.me)
async def reset_tts_voices(client, message):
    """Reset TTS voice preferences to defaults"""
    try:
        user_id = message.from_user.id
        
        # Reset to default voices
        db.tts_voice_prefs.update_one(
            {'user_id': user_id},
            {'$set': {
                'voices.gemini': 'Kore',
                'voices.groq': 'Fritz-PlayAI',
                'voices.cloudflare': None
            }},
            upsert=True
        )
        
        await message.edit(
            "🔄 **TTS voices reset to defaults!**\n\n"
            "**New defaults:**\n"
            "• **Gemini:** `Kore` (Firm and confident)\n"
            "• **Groq:** `Fritz-PlayAI`\n"
            "• **Cloudflare:** No voice selection\n\n"
            "Use `.ttsvoice <provider> <voice>` to set custom voices."
        )

    except Exception as e:
        await message.edit(f"⚠️ **Error resetting voices:** `{str(e)}`")
