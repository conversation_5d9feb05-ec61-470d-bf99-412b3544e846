from pyrogram import Client
from helpers.config import config

# Get Telegram credentials from config
creds = config.get_telegram_credentials()
api_id = creds['api_id']
api_hash = creds['api_hash']
session = creds['session']

# Convert api_id to int if it's a string
if isinstance(api_id, str) and api_id.isdigit():
    api_id = int(api_id)

# Get proxy configuration from config
proxy = config.get_proxy_config()

plugins = dict(root="plugins")

# Initialize and run the client
Client(session, api_id=api_id, api_hash=api_hash,
       proxy=proxy,
       plugins=plugins).run()
