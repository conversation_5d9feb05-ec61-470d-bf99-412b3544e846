#!/usr/bin/env python3
"""
Migration script to convert config.yaml to .env format
"""
import yaml
import os
from pathlib import Path

def migrate_config():
    """Convert config.yaml to .env format"""
    config_file = Path('config.yaml')
    env_file = Path('.env')
    
    if not config_file.exists():
        print("❌ config.yaml not found. Nothing to migrate.")
        return
    
    if env_file.exists():
        response = input("⚠️  .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Migration cancelled.")
            return
    
    print("🔄 Converting config.yaml to .env format...")
    
    # Load existing config
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    
    # Create .env content
    env_content = []
    env_content.append("# Telegram Configuration")
    
    # Telegram credentials
    if 'api_credentials' in config and 'telegram' in config['api_credentials']:
        tg = config['api_credentials']['telegram']
        env_content.append(f"TELEGRAM_API_ID={tg.get('api_id', '')}")
        env_content.append(f"TELEGRAM_API_HASH={tg.get('api_hash', '')}")
        env_content.append(f"TELEGRAM_SESSION={tg.get('session', 'my_account')}")
    
    env_content.append("")
    env_content.append("# AI Provider API Keys")
    
    # AI provider credentials
    if 'api_credentials' in config and 'ai_providers' in config['api_credentials']:
        providers = config['api_credentials']['ai_providers']
        
        if 'google' in providers:
            env_content.append(f"GOOGLE_API_KEY={providers['google'].get('api_key', '')}")
        
        if 'gemini' in providers:
            env_content.append(f"GEMINI_API_KEY={providers['gemini'].get('api_key', '')}")
        
        if 'groq' in providers:
            env_content.append(f"GROQ_API_KEY={providers['groq'].get('api_key', '')}")
        
        if 'openrouter' in providers:
            env_content.append(f"OPENROUTER_API_KEY={providers['openrouter'].get('api_key', '')}")
        
        if 'minitool' in providers:
            env_content.append(f"MINITOOL_API_KEY={providers['minitool'].get('api_key', '')}")
        
        if 'cloudflare' in providers:
            cf = providers['cloudflare']
            env_content.append("")
            env_content.append("# Cloudflare AI Configuration")
            env_content.append(f"CLOUDFLARE_ACCOUNT_ID={cf.get('account_id', '')}")
            env_content.append(f"CLOUDFLARE_API_KEY={cf.get('api_key', '')}")
    
    # Proxy configuration
    if 'proxy' in config:
        proxy = config['proxy']
        env_content.append("")
        env_content.append("# Proxy Configuration")
        env_content.append(f"PROXY_ENABLED={str(proxy.get('enabled', False)).lower()}")
        env_content.append(f"PROXY_SCHEME={proxy.get('scheme', 'socks5')}")
        env_content.append(f"PROXY_HOSTNAME={proxy.get('hostname', 'localhost')}")
        env_content.append(f"PROXY_PORT={proxy.get('port', '10808')}")
    
    # Write .env file
    with open(env_file, 'w') as f:
        f.write('\n'.join(env_content))
    
    print(f"✅ Successfully created {env_file}")
    print(f"📝 You can now delete {config_file} if you want")
    print("🔒 Make sure to add .env to your .gitignore file to keep your credentials secure")

if __name__ == "__main__":
    migrate_config()
