#!/usr/bin/env python3
"""
Test Bitcoin dominance data fetching and analysis
"""
import requests
import json
from datetime import datetime

def get_bitcoin_dominance_data():
    """Fetch Bitcoin dominance data from multiple sources"""
    try:
        dominance_data = {}
        
        # Try CoinGecko global data first
        try:
            url = "https://api.coingecko.com/api/v3/global"
            headers = {"User-Agent": "CryptoAnalysisBot/1.0"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            global_data = response.json()
            
            if 'data' in global_data:
                data = global_data['data']
                dominance_data.update({
                    "btc_dominance": data.get('market_cap_percentage', {}).get('btc'),
                    "eth_dominance": data.get('market_cap_percentage', {}).get('eth'),
                    "total_market_cap": data.get('total_market_cap', {}).get('usd'),
                    "total_volume_24h": data.get('total_volume', {}).get('usd'),
                    "active_cryptocurrencies": data.get('active_cryptocurrencies'),
                    "markets": data.get('markets'),
                    "market_cap_change_24h": data.get('market_cap_change_percentage_24h_usd')
                })
                
        except Exception as e:
            print(f"Warning: Could not fetch global data: {str(e)}")
        
        # Get historical dominance data (simplified)
        try:
            # Calculate approximate historical dominance based on BTC vs total market
            btc_url = "https://api.coingecko.com/api/v3/coins/bitcoin/market_chart"
            params = {"vs_currency": "usd", "days": "30", "interval": "daily"}
            
            response = requests.get(btc_url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            btc_data = response.json()
            
            if 'market_caps' in btc_data and btc_data['market_caps']:
                # Get recent market cap data points
                recent_caps = btc_data['market_caps'][-7:]  # Last 7 days
                dominance_data['historical_btc_caps'] = [
                    {"date": datetime.fromtimestamp(cap[0]/1000).strftime("%Y-%m-%d"), 
                     "market_cap": cap[1]} 
                    for cap in recent_caps
                ]
                
        except Exception as e:
            print(f"Warning: Could not fetch historical dominance data: {str(e)}")
        
        return dominance_data
        
    except Exception as e:
        print(f"Warning: Could not fetch Bitcoin dominance data: {str(e)}")
        return {}

def format_large_number(num):
    """Format large numbers in a readable way"""
    if num >= 1e12:
        return f"${num/1e12:.2f}T"
    elif num >= 1e9:
        return f"${num/1e9:.2f}B"
    elif num >= 1e6:
        return f"${num/1e6:.2f}M"
    elif num >= 1e3:
        return f"${num/1e3:.2f}K"
    else:
        return f"${num:.2f}"

def test_bitcoin_dominance_data():
    """Test Bitcoin dominance data fetching"""
    print("👑 Testing Bitcoin Dominance Data Fetching\n")
    
    # Fetch dominance data
    print("📊 Fetching Bitcoin dominance data...")
    dominance_data = get_bitcoin_dominance_data()
    
    if dominance_data:
        print("✅ Successfully fetched dominance data!\n")
        
        # Display current market state
        print("📈 CURRENT MARKET STATE:")
        print("="*40)
        
        btc_dom = dominance_data.get('btc_dominance')
        eth_dom = dominance_data.get('eth_dominance')
        total_cap = dominance_data.get('total_market_cap')
        total_vol = dominance_data.get('total_volume_24h')
        active_cryptos = dominance_data.get('active_cryptocurrencies')
        markets = dominance_data.get('markets')
        cap_change = dominance_data.get('market_cap_change_24h')
        
        if btc_dom:
            print(f"🪙 Bitcoin Dominance: {btc_dom:.2f}%")
        if eth_dom:
            print(f"⚡ Ethereum Dominance: {eth_dom:.2f}%")
        if total_cap:
            print(f"💰 Total Market Cap: {format_large_number(total_cap)}")
        if total_vol:
            print(f"📊 24h Volume: {format_large_number(total_vol)}")
        if active_cryptos:
            print(f"🔢 Active Cryptocurrencies: {active_cryptos:,}")
        if markets:
            print(f"🏪 Markets: {markets:,}")
        if cap_change:
            print(f"📈 Market Cap Change 24h: {cap_change:+.2f}%")
        
        # Historical data
        if 'historical_btc_caps' in dominance_data:
            print(f"\n📅 HISTORICAL BTC MARKET CAPS (Last 7 days):")
            print("="*40)
            for cap_data in dominance_data['historical_btc_caps']:
                date = cap_data['date']
                market_cap = format_large_number(cap_data['market_cap'])
                print(f"  {date}: {market_cap}")
        
        # Analysis insights
        print(f"\n🔍 DOMINANCE ANALYSIS INSIGHTS:")
        print("="*40)
        
        if btc_dom:
            if btc_dom > 60:
                print("👑 Bitcoin is HIGHLY dominant (>60%)")
                print("   → Altcoins likely underperforming")
                print("   → Market in Bitcoin accumulation phase")
            elif btc_dom > 50:
                print("⚖️ Bitcoin has STRONG dominance (50-60%)")
                print("   → Balanced market with BTC leadership")
                print("   → Some altcoin opportunities emerging")
            elif btc_dom > 40:
                print("📊 Bitcoin has MODERATE dominance (40-50%)")
                print("   → Altcoin season potential")
                print("   → Diversified crypto market")
            else:
                print("🌈 Bitcoin has LOW dominance (<40%)")
                print("   → Strong altcoin season")
                print("   → High risk/reward environment")
        
        # Sample analysis data structure
        print(f"\n📋 SAMPLE ANALYSIS DATA STRUCTURE:")
        print("="*40)
        
        sample_analysis = {
            "analysis_type": "bitcoin_dominance",
            "timestamp": datetime.now().isoformat(),
            "dominance_data": dominance_data,
            "data_quality": {
                "dominance_data_available": True,
                "analysis_confidence": "high"
            }
        }
        
        print(json.dumps(sample_analysis, indent=2)[:500] + "...")
        
    else:
        print("❌ Failed to fetch dominance data")
    
    print(f"\n🎯 BITCOIN DOMINANCE COMMAND FEATURES:")
    print("="*50)
    features = [
        "✅ Real-time Bitcoin dominance percentage",
        "✅ Ethereum dominance for comparison", 
        "✅ Total cryptocurrency market cap",
        "✅ 24h trading volume across all cryptos",
        "✅ Number of active cryptocurrencies",
        "✅ Historical Bitcoin market cap trends",
        "✅ Market cap change percentage (24h)",
        "✅ Comprehensive market cycle analysis",
        "✅ Altcoin impact assessment",
        "✅ Portfolio allocation recommendations",
        "✅ Trading opportunities based on dominance",
        "✅ Multi-language support (Persian, English, etc.)"
    ]
    
    for feature in features:
        print(feature)
    
    print(f"\n💡 USAGE EXAMPLES:")
    print("="*30)
    print("• .btcd              → Basic dominance analysis")
    print("• .btcd -g           → Analysis with Gemini AI")
    print("• .btcd -g -fa       → Persian analysis with Gemini")
    print("• .btcd -q -en       → English analysis with Groq")
    print("• .btcd help         → Show detailed help")

if __name__ == "__main__":
    test_bitcoin_dominance_data()
