# helpers/models.py
import os
import requests
import logging
from typing import Dict, List, Optional, Any, Union

# Model options (commented out for reference)
# model = "llama-3.3-70b-versatile"
# model = "qwen-2.5-32b"
# model = "gemma2-9b-it"
# model = "llama-3.3-70b-specdec"
# model = "llama-3.3-70b-versatile"
# model = "deepseek-r1-distill-llama-70b"
# model = "llama3-70b-8192"
# openrouter_model = "cognitivecomputations/dolphin3.0-r1-mistral-24b:free"
# openrouter_model = "google/gemini-2.0-pro-exp-02-05:free"
# openrouter_model = "qwen/qwen2.5-vl-72b-instruct:free"
# openrouter_model = "mistralai/mistral-small-24b-instruct-2501:free"
# openrouter_model = "deepseek/deepseek-r1-distill-llama-70b:free"
# openrouter_model = "deepseek/deepseek-r1:free"
# openrouter_model = "sophosympatheia/rogue-rose-103b-v0.2:free"
# openrouter_model = "deepseek/deepseek-chat:free"
# openrouter_model = "nvidia/llama-3.1-nemotron-70b-instruct:free"
# openrouter_model = "google/gemini-2.0-flash-thinking-exp:free"
# openrouter_model = "google/gemini-2.0-flash-lite-preview-02-05:free"
# openrouter_model = "google/gemini-2.0-flash-exp:free"
# openrouter_model = "google/gemini-2.0-pro-exp-02-05:free"

# Ollama models (commented out for reference)
# ❯ ollama list
# NAME                   ID              SIZE      MODIFIED
# deepseek-coder:6.7b    ce298d984115    3.8 GB    10 days ago
# qwen2.5-coder:3b       e7149271c296    1.9 GB    10 days ago
# llama3.2:3b            a80c4f17acd5    2.0 GB    10 days ago
# llama3.2:1b            baf6a787fdff    1.3 GB    10 days ago
# deepseek-r1:1.5b       a42b25d8c10a    1.1 GB    10 days ago
# tinyllama:latest       2644915ede35    637 MB    11 days ago
# tinyllama:1.1b         2644915ede35    637 MB    11 days ago

# Functions to fetch models from various APIs
def get_groq_models(client) -> List[str]:
    """
    Fetch available models from the Groq API.

    Args:
        client: The initialized Groq client

    Returns:
        List of model names
    """
    try:
        models = client.models.list()
        # Extract model names
        groq_models = [model.id for model in models.data]
        # Sort models for better display
        groq_models.sort()
        return groq_models
    except Exception as e:
        logging.error(f"Failed to fetch Groq models: {str(e)}")
        # Return a default list of models if API call fails
        return [
            'llama-3.3-70b-versatile',
            'qwen-2.5-32b',
            'gemma2-9b-it',
            'llama-3.3-70b-specdec',
            'deepseek-r1-distill-llama-70b',
            'llama3-70b-8192'
        ]

def get_gemini_models(client, include_descriptions=False) -> Union[List[dict], List[str]]:
    """
    Fetch available models from the Gemini API.

    Args:
        client: The initialized Gemini client
        include_descriptions: Whether to include model descriptions

    Returns:
        If include_descriptions is True, returns a list of dictionaries with model info
        Otherwise, returns a list of model names
    """
    try:
        models = client.models.list()
        # Filter for Gemini models only and clean up the names
        gemini_models = []
        gemini_models_with_info = []

        for model in models:
            if 'gemini' in model.name.lower():
                # Extract just the model name without the 'models/' prefix
                model_name = model.name.replace('models/', '')
                gemini_models.append(model_name)

                if include_descriptions:
                    model_info = {
                        'name': model_name,
                        'display_name': getattr(model, 'display_name', None),
                        'description': getattr(model, 'description', None)
                    }
                    gemini_models_with_info.append(model_info)

        # Sort models for better display
        gemini_models.sort()
        if include_descriptions:
            # Sort models with info by name
            gemini_models_with_info.sort(key=lambda x: x['name'])
            return gemini_models_with_info
        else:
            return gemini_models
    except Exception as e:
        logging.error(f"Failed to fetch Gemini models: {str(e)}")
        # Return a default list of models if API call fails
        default_models = [
            'gemini-1.5-pro',
            'gemini-1.5-flash',
            'gemini-1.5-flash-8b',
            'gemini-2.0-pro-exp',
            'gemini-2.0-flash'
        ]

        if include_descriptions:
            return [{'name': name, 'display_name': None, 'description': None} for name in default_models]
        else:
            return default_models

def get_cloudflare_models(client) -> List[str]:
    """
    Fetch available models from the Cloudflare API.

    Args:
        client: The initialized Cloudflare client

    Returns:
        List of model names
    """
    try:
        # The Cloudflare API for listing models is not publicly documented.
        # We will use a predefined list of models for now.
        # This can be updated if the API becomes available.
        return [
            '@cf/meta/llama-2-7b-chat-int8',
            '@cf/meta/llama-2-7b-chat-fp16',
            '@cf/meta/llama-3-8b-instruct',
            '@cf/meta/llama-3.1-8b-instruct',
            '@cf/meta/llama-3.1-8b-instruct-fast',
            '@cf/meta/llama-3.1-8b-instruct-fp8',
            '@cf/meta/llama-3.1-8b-instruct-awq',
            '@cf/meta/llama-3.1-70b-instruct',
            '@cf/meta/llama-3.2-1b-instruct',
            '@cf/meta/llama-3.2-3b-instruct',
            '@cf/meta/llama-3.2-11b-vision-instruct',
            '@cf/meta/llama-3.3-70b-instruct-fp8-fast',
            '@cf/meta/llama-guard-3-8b',
            '@cf/mistral/mistral-7b-instruct-v0.1',
            '@cf/mistral/mistral-7b-instruct-v0.2',
            '@cf/mistral/mistral-7b-instruct-v0.2-lora',
            '@cf/google/gemma-2b-it-lora',
            '@cf/google/gemma-7b-it',
            '@cf/google/gemma-7b-it-lora',
            '@cf/thebloke/deepseek-coder-6.7b-base-awq',
            '@cf/thebloke/deepseek-coder-6.7b-instruct-awq',
            '@cf/deepseek-ai/deepseek-math-7b-instruct',
            '@cf/deepseek-ai/deepseek-r1-distill-qwen-32b',
            '@cf/thebloke/discolm-german-7b-v1-awq',
            '@cf/tiiuae/falcon-7b-instruct',
            '@cf/nousresearch/hermes-2-pro-mistral-7b',
            '@cf/openchat/openchat-3.5-0106',
            '@cf/microsoft/phi-2',
            '@cf/qwen/qwen1.5-0.5b-chat',
            '@cf/qwen/qwen1.5-1.8b-chat',
            '@cf/qwen/qwen1.5-7b-chat-awq',
            '@cf/qwen/qwen1.5-14b-chat-awq',
            '@cf/defog/sqlcoder-7b-2',
            '@cf/nexusflow/starling-lm-7b-beta',
            '@cf/tinyllama/tinyllama-1.1b-chat-v1.0',
            '@cf/fblgit/una-cybertron-7b-v2-bf16',
            '@cf/meta/m2m100-1.2b',
            '@cf/myshell-ai/melotts',
            '@cf/lykon/dreamshaper-8-lcm',
            '@cf/black-forest-labs/flux-1-schnell',
            '@cf/runwayml/stable-diffusion-v1-5-img2img',
            '@cf/runwayml/stable-diffusion-v1-5-inpainting',
            '@cf/stability-ai/stable-diffusion-xl-base-1.0',
            '@cf/bytedance/stable-diffusion-xl-lightning'
        ]
    except Exception as e:
        logging.error(f"Failed to fetch Cloudflare models: {str(e)}")
        return []

# Provider models dictionary
providers = {
    'groq': {
        'default_model': 'llama-3.3-70b-versatile',
        'models': []
    },
    'cloudflare': {
        'default_model': '@cf/meta/llama-2-7b-chat-int8',
        'models': []
    },
    'google': {
        'default_model': 'gemini-2.5-flash',
        'models': []
    }
}

# Client classes for different AI providers
class MiniToolAIClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.minitoolai.com/v1/chat"

    def chat(self, model: str, messages: list, temperature: float, max_tokens: int):
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }
        response = requests.post(self.base_url, json=payload, headers=headers)
        return response.json()

class OpenRouterClient:
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key not provided or found in environment variable 'OPENROUTER_API_KEY'")
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

    def chat_completions_create(self, model: str, messages, temperature: float = 0.7, max_tokens: int = 3000, timeout: int = 30):
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"OpenRouter API request failed: {str(e)}")


