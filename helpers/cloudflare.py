import os
import requests

class CloudflareAIClient:
    def __init__(self, account_id: str, api_token: str):
        if not account_id or not api_token:
            raise ValueError("Both account_id and api_token must be provided")

        self.account_id = account_id
        self.api_token = api_token
        self.base_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/ai/run/"
        self.headers = {
            "Authorization": f"Bearer {api_token}",
            "Content-Type": "application/json"
        }

    def run_model(self, model: str, messages: dict | list):
        """Execute an AI model on Cloudflare Workers AI."""
        if not model or not messages:
            raise ValueError("Both model and messages must be provided")

        try:
            response = requests.post(
                f"{self.base_url}{model}",
                headers=self.headers,
                json=messages,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f": {error_detail}"
                except:
                    error_msg += f": {response.text[:200]}"
                raise ValueError(error_msg)

            content_type = response.headers.get('Content-Type', '')

            if 'image' in content_type:
                return response.content
            elif 'application/json' in content_type:
                return response.json()
            else:
                raise ValueError(f"Unexpected content type: {content_type}")

        except requests.exceptions.RequestException as e:
            raise ValueError(f"Request failed: {str(e)}")
