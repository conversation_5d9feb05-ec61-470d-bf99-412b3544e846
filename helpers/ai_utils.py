#helpers/ai_utils.py
from helpers import (
    groq_cli,
    gemini_cli, 
    OpenRouterClient,
    minitoolai_client,
    gemini_safety_settings,
    cloudflare_ai
)
from google.genai import types
from google.genai.types import GenerateContentConfig, Tool, FunctionDeclaration, GoogleSearch  # Add GoogleSearch here
from typing import Any, Dict, List, Optional
from pudb import set_trace


class AIResponseGenerator:
    def __init__(self, provider: str, model: str):
        """Initialize the AI response generator with a provider and model."""
        self.provider = provider
        self.model = model

        # Define common function declarations
        self.search_web_declaration = {
            "name": "search_web",
            "description": "Search the web for real-time information",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to execute"
                    },
                    "num_results": {
                        "type": "integer",
                        "description": "Number of results to return",
                        "default": 3
                    }
                },
                "required": ["query"]
            }
        }

        self.generate_image_declaration = {
            "name": "generate_image",
            "description": "Generate an image based on text description",
            "parameters": {
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "Detailed description of the image to generate"
                    },
                    "style": {
                        "type": "string",
                        "enum": ["realistic", "artistic", "cartoon", "sketch"],
                        "description": "Style of the image to generate"
                    },
                    "size": {
                        "type": "string",
                        "enum": ["256x256", "512x512", "1024x1024"],
                        "default": "512x512"
                    }
                },
                "required": ["prompt"]
            }
        }

    async def generate_response(
        self,
        system_prompt: str,
        user_query: str,
        temperature: float = 0.7,
        max_tokens: int = 3000,
        use_search: bool = False,
        image_path: str = None,
    ):
        """Generate an AI response based on the provider."""
        if self.provider == "cloudflare":
            try:
                if cloudflare_ai is None:
                    raise ValueError("Cloudflare AI client is not properly initialized")

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query}
                ]

                response = cloudflare_ai.run_model(
                    model=self.model,
                    messages=messages
                )

                # Add debug logging
                # print(f"Cloudflare Response: {response}")  # Debug line

                if not response:
                    raise ValueError("Empty response from Cloudflare AI")

                if isinstance(response, dict):
                    if 'result' in response and 'response' in response['result']:
                        return response['result']['response']
                    elif 'errors' in response:
                        raise ValueError(f"Cloudflare API error: {response['errors']}")
                    else:
                        # If response has different structure, return the whole response as string
                        return str(response)
                else:
                    return str(response)

            except Exception as e:
                raise ValueError(f"Cloudflare AI error: {str(e)}")
        elif self.provider == "minitoolai":
            response = minitoolai_client.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query},
                ],
                temperature=temperature,
                max_tokens=max_tokens,
            )
            return response["choices"][0]["message"]["content"]

        elif self.provider == "groq":
            response = groq_cli.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query},
                ],
                temperature=temperature,
                max_tokens=max_tokens,
            )
            return response.choices[0].message.content
        elif self.provider == "google":

            # Configure Google Search tool
            google_search_tool = Tool(
                google_search=GoogleSearch()
            )

            # Configure generation settings
            config = GenerateContentConfig(
                system_instruction=system_prompt,
                tools=[google_search_tool] if use_search else None,
                response_modalities=["TEXT"],
                safety_settings=gemini_safety_settings
            )

            # Handle image if provided
            if image_path:
                try:
                    from PIL import Image
                    # Open the image
                    image = Image.open(image_path)
                    # Generate content with both image and text
                    response = gemini_cli.models.generate_content(
                        model=self.model,
                        config=config,
                        contents=[image, user_query],
                    )
                except Exception as e:
                    # If image processing fails, fall back to text-only
                    print(f"Error processing image: {str(e)}")
                    response = gemini_cli.models.generate_content(
                        model=self.model,
                        config=config,
                        contents=[user_query],
                    )
            else:
                # Text-only query
                response = gemini_cli.models.generate_content(
                    model=self.model,
                    config=config,
                    contents=[user_query],
                )


            # Get response text
            response_text = response.text.strip()

            # Append search results if enabled
            if use_search and response.candidates[0].grounding_metadata:
                search_data = response.candidates[0].grounding_metadata.search_entry_point

                if search_data and hasattr(search_data, "rendered_content"):
                    search_results = search_data.rendered_content.strip()
                    search_summary = search_results[:300] + "..." if len(search_results) > 300 else search_results

                    # Try to extract a source URL
                    search_url = search_data.uri if hasattr(search_data, "uri") else None
                    source_text = f"[🔗 Source]({search_url})" if search_url else ""

                    response_text += f"\n\n---\n🔍 **Search Results:**\n{search_summary}\n{source_text}"

            return response_text




            # if use_search and response.candidates[0].grounding_metadata:
            #     search_data = response.candidates[0].grounding_metadata.search_entry_point

            #     if search_data and hasattr(search_data, "rendered_content"):
            #         search_results = search_data.rendered_content.strip()
            #         search_summary = search_results[:300] + "..." if len(search_results) > 300 else search_results

            #         response_text += f"\n\n---\n🔍 **Search Results:**\n{search_summary}"

            # return response_text





            # # Get the response text
            # response_text = response.text

            # # If search was used, append the search results
            # if use_search and response.candidates[0].grounding_metadata:
            #     search_results = response.candidates[0].grounding_metadata.search_entry_point.rendered_content
            #     response_text += "\n\nSearch Results:\n" + search_results

            # return response_text
        elif self.provider == "openrouter":
            openrouter_client = OpenRouterClient()
            response = openrouter_client.chat_completions_create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query},
                ],
                temperature=temperature,
                max_tokens=max_tokens,
            )
            return response["choices"][0]["message"]["content"]
        elif self.provider == "ollama":
            response = ollama.chat(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_query},
                ],
                options={"temperature": temperature, "num_predict": max_tokens},
            )
            return response["message"]["content"]
        else:
            raise ValueError(f"Unsupported AI provider: {self.provider}")

    async def generate_response_with_tools(
        self,
        system_prompt: str,
        user_query: str,
        temperature: float = 0.7,
        max_tokens: int = 3000,
        enable_tools: List[str] = None
    ) -> Dict[str, Any]:
        """Generate an AI response with optional tool/function calling capabilities."""
        if self.provider == "google":
            tools = []

            # Add requested tools
            if enable_tools:
                if "search" in enable_tools:
                    tools.append(Tool(function_declarations=[self.search_web_declaration]))
                if "image" in enable_tools:
                    tools.append(Tool(function_declarations=[self.generate_image_declaration]))

                # Configure tool settings
                tool_config = types.ToolConfig(
                    function_calling_config=types.FunctionCallingConfig(
                        mode="AUTO"  # Let model decide when to use tools
                    )
                )

            # Configure generation settings
            config = GenerateContentConfig(
                system_instruction=system_prompt,
                tools=tools if tools else None,
                tool_config=tool_config if tools else None,
                response_modalities=["TEXT"],
                safety_settings=gemini_safety_settings
            )

            response = gemini_cli.models.generate_content(
                model=self.model,
                config=config,
                contents=[user_query],
            )

            # Handle function calls if present
            result = {
                "text": response.text,
                "function_calls": []
            }

            if hasattr(response.candidates[0].content.parts[0], 'function_call'):
                function_call = response.candidates[0].content.parts[0].function_call
                result["function_calls"].append({
                    "name": function_call.name,
                    "args": function_call.args
                })

                # Execute function calls (implement actual functions)
                if function_call.name == "search_web":
                    # Implement web search logic
                    pass
                elif function_call.name == "generate_image":
                    # Implement image generation logic
                    pass

            return result

        # Handle other providers...
        else:
            return await self.generate_response(
                system_prompt=system_prompt,
                user_query=user_query,
                temperature=temperature,
                max_tokens=max_tokens
            )


def parse_provider(cmd, default_provider="groq"):
    """Parse the command to extract the AI provider."""
    if len(cmd) > 1 and cmd[1] in ["-q", "-g", "-o", "-r", "-m", "-c"]:
        provider = {
            "-q": "groq",
            "-g": "google",
            "-o": "ollama",
            "-r": "openrouter",
            "-m": "minitoolai",
            "-c": "cloudflare"
        }[cmd[1]]
    else:
        provider = default_provider
    return provider

