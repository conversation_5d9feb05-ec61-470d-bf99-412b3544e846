# Configuration Migration Guide

## Overview

The configuration system has been simplified from a complex YAML-based approach to a simple `.env` file approach. This makes the configuration more straightforward and follows common practices.

## What Changed

### Before (config.yaml)
```yaml
api_credentials:
  telegram:
    api_id: "2378750"
    api_hash: "c8306ebdea575874aea716c7a7531cb8"
    session: "my_account"
  ai_providers:
    google:
      api_key: "AIzaSy..."
    groq:
      api_key: "gsk_..."
```

### After (.env)
```bash
TELEGRAM_API_ID=2378750
TELEGRAM_API_HASH=c8306ebdea575874aea716c7a7531cb8
TELEGRAM_SESSION=my_account
GOOGLE_API_KEY=AIzaSy...
GROQ_API_KEY=gsk_...
```

## Benefits

1. **Simpler**: No nested YAML structure
2. **Standard**: `.env` files are industry standard
3. **Secure**: Easier to keep credentials out of version control
4. **Flexible**: Environment variables can override .env values
5. **No Dependencies**: Removed PyYAML dependency

## Migration Steps

### Automatic Migration
If you have an existing `config.yaml`:
```bash
python migrate_config.py
```

### Manual Setup
1. Copy the template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your credentials

## Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `TELEGRAM_API_ID` | Telegram API ID | `2378750` |
| `TELEGRAM_API_HASH` | Telegram API Hash | `c8306...` |
| `TELEGRAM_SESSION` | Session name | `my_account` |
| `GOOGLE_API_KEY` | Google/Gemini API key | `AIzaSy...` |
| `GROQ_API_KEY` | Groq API key | `gsk_...` |
| `OPENROUTER_API_KEY` | OpenRouter API key | `sk-or...` |
| `CLOUDFLARE_ACCOUNT_ID` | Cloudflare account ID | `892de8...` |
| `CLOUDFLARE_API_KEY` | Cloudflare API key | `v3Z0J...` |
| `PROXY_ENABLED` | Enable proxy | `true/false` |
| `PROXY_SCHEME` | Proxy scheme | `socks5` |
| `PROXY_HOSTNAME` | Proxy hostname | `localhost` |
| `PROXY_PORT` | Proxy port | `10808` |

## Code Changes

### ConfigManager
- Removed YAML dependency
- Simplified to use environment variables only
- Added convenience methods for common configurations

### Usage
```python
from helpers.config import config

# Get Telegram credentials
creds = config.get_telegram_credentials()

# Get API keys
google_key = config.get_google_api_key()
groq_key = config.get_groq_api_key()

# Get proxy config
proxy = config.get_proxy_config()
```

## Security Notes

- `.env` files are automatically ignored by git
- Never commit `.env` files to version control
- Use `.env.example` as a template for others
- Environment variables take precedence over `.env` file values

## Troubleshooting

### Missing credentials error
Make sure your `.env` file exists and contains the required variables.

### Old config still being used
Delete or rename `config.yaml` to ensure the new system is used.

### Environment variables not loading
Check that your `.env` file is in the project root directory.
