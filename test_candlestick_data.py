#!/usr/bin/env python3
"""
Test script for candlestick data fetching
"""
import requests
import json
from datetime import datetime

def get_coingecko_id(symbol):
    """Get CoinGecko ID for a symbol"""
    # Common symbol to CoinGecko ID mapping
    symbol_map = {
        'BTC': 'bitcoin',
        'ETH': 'ethereum',
        'ADA': 'cardano',
        'DOT': 'polkadot',
        'LINK': 'chainlink',
        'LTC': 'litecoin',
        'XRP': 'ripple',
        'BCH': 'bitcoin-cash',
        'BNB': 'binancecoin',
        'SOL': 'solana',
        'MATIC': 'matic-network',
        'AVAX': 'avalanche-2',
        'ATOM': 'cosmos',
        'DOGE': 'dogecoin',
        'SHIB': 'shiba-inu',
        'UNI': 'uniswap',
        'AAVE': 'aave',
        'SUSHI': 'sushi',
        'COMP': 'compound-governance-token',
        'MKR': 'maker'
    }
    
    return symbol_map.get(symbol.upper(), symbol.lower())

def get_candlestick_data(symbol, days=7):
    """Fetch candlestick data from CoinGecko API (free, no API key required)"""
    try:
        coingecko_id = get_coingecko_id(symbol)
        
        # CoinGecko OHLC endpoint (free)
        url = f"https://api.coingecko.com/api/v3/coins/{coingecko_id}/ohlc"
        params = {
            "vs_currency": "usd",
            "days": days
        }
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        ohlc_data = response.json()
        
        # Convert to our format
        candles = []
        for ohlc in ohlc_data[-20:]:  # Last 20 candles for analysis
            timestamp, open_price, high, low, close = ohlc
            candles.append({
                "timestamp": timestamp,
                "date": datetime.fromtimestamp(timestamp/1000).strftime("%Y-%m-%d %H:%M"),
                "open": open_price,
                "high": high,
                "low": low,
                "close": close
            })
        
        return candles
        
    except Exception as e:
        print(f"Warning: Could not fetch candlestick data: {str(e)}")
        return []

def test_candlestick_fetching():
    """Test fetching candlestick data for various cryptocurrencies"""
    print("🕯️ Testing candlestick data fetching...\n")
    
    test_symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOGE']
    
    for symbol in test_symbols:
        print(f"Testing {symbol}...")
        try:
            candles = get_candlestick_data(symbol, days=7)
            
            if candles:
                print(f"✅ {symbol}: Fetched {len(candles)} candlesticks")
                
                # Show sample data
                latest = candles[-1]
                print(f"   Latest: {latest['date']}")
                print(f"   OHLC: O:{latest['open']:.2f} H:{latest['high']:.2f} L:{latest['low']:.2f} C:{latest['close']:.2f}")
                
                # Calculate some basic indicators
                if len(candles) >= 2:
                    price_change = latest['close'] - candles[-2]['close']
                    price_change_pct = (price_change / candles[-2]['close']) * 100
                    print(f"   24h Change: {price_change:+.2f} ({price_change_pct:+.2f}%)")
                
            else:
                print(f"❌ {symbol}: No candlestick data received")
                
        except Exception as e:
            print(f"❌ {symbol}: Error - {str(e)}")
        
        print()
    
    print("🎯 Testing complete!")

def test_sample_analysis_data():
    """Test creating sample analysis data like the plugin would"""
    print("📊 Testing sample analysis data creation...\n")
    
    # Simulate what the plugin does
    symbol = "BTC"
    candles = get_candlestick_data(symbol, days=7)
    
    # Create sample market data structure
    sample_market_data = {
        "symbol": symbol,
        "current_price": 45000.50,
        "percent_change_1h": 1.25,
        "percent_change_24h": -2.15,
        "percent_change_7d": 5.75,
        "volume_24h": 25000000000,
        "market_cap": 850000000000,
        "candles": candles[:5]  # Show first 5 for brevity
    }
    
    print("Sample data that would be sent to AI:")
    print(json.dumps(sample_market_data, indent=2))
    
    if candles:
        print(f"\n✅ Successfully integrated {len(candles)} candlesticks into market data")
    else:
        print("\n⚠️ No candlestick data available")

if __name__ == "__main__":
    print("🚀 Testing cryptocurrency candlestick data integration...\n")
    
    try:
        test_candlestick_fetching()
        print("\n" + "="*50 + "\n")
        test_sample_analysis_data()
        
        print("\n🎉 All tests completed!")
        print("\n📋 Summary:")
        print("- Candlestick data is now fetched from CoinGecko (free API)")
        print("- No additional API key required for candlestick data")
        print("- Data includes OHLC (Open, High, Low, Close) for technical analysis")
        print("- AI will now have comprehensive data for proper analysis")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
