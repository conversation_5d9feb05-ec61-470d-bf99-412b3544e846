#!/usr/bin/env python3
"""
Test the command parsing logic for the crypto plugin
"""

def test_crypto_parsing():
    """Test different command formats"""
    
    def parse_crypto_command(cmd_parts):
        """Simulate the parsing logic from the crypto plugin"""
        symbol = None
        provider_args = []
        language = None

        # Language mapping
        language_map = {
            '-fa': 'persian',
            '-en': 'english',
            '-ar': 'arabic',
            '-es': 'spanish',
            '-fr': 'french',
            '-de': 'german',
            '-it': 'italian',
            '-pt': 'portuguese',
            '-ru': 'russian',
            '-zh': 'chinese',
            '-ja': 'japanese',
            '-ko': 'korean'
        }

        # Find the symbol, provider flags, and language flags
        for arg in cmd_parts[1:]:  # Skip the command name
            if arg in language_map:
                language = language_map[arg]
            elif arg.startswith('-'):
                provider_args.append(arg)
            else:
                symbol = arg.upper()

        return symbol, provider_args, language
    
    # Test cases
    test_cases = [
        (['.crypto', 'BTC'], ('BTC', [], None)),
        (['.crypto', 'BTC', '-g'], ('BTC', ['-g'], None)),
        (['.crypto', '-g', 'BTC'], ('BTC', ['-g'], None)),
        (['.crypto', '-q', 'ETH'], ('ETH', ['-q'], None)),
        (['.crypto', 'ETH', '-q'], ('ETH', ['-q'], None)),
        (['.crypto', '-g', 'BTC', '-fa'], ('BTC', ['-g'], 'persian')),
        (['.crypto', 'BTC', '-g', '-fa'], ('BTC', ['-g'], 'persian')),
        (['.crypto', '-fa', 'BTC', '-g'], ('BTC', ['-g'], 'persian')),
        (['.crypto', 'ETH', '-q', '-en'], ('ETH', ['-q'], 'english')),
        (['.crypto', '-ar', 'ADA', '-c'], ('ADA', ['-c'], 'arabic')),
        (['.crypto', 'DOGE', '-g', '-es'], ('DOGE', ['-g'], 'spanish')),
    ]
    
    print("🧪 Testing crypto command parsing logic...\n")
    
    for i, (input_cmd, expected) in enumerate(test_cases, 1):
        result = parse_crypto_command(input_cmd)
        status = "✅" if result == expected else "❌"
        
        print(f"Test {i}: {status}")
        print(f"  Input: {' '.join(input_cmd)}")
        print(f"  Expected: symbol='{expected[0]}', provider_args={expected[1]}, language={expected[2]}")
        print(f"  Got:      symbol='{result[0]}', provider_args={result[1]}, language={result[2]}")
        
        if result != expected:
            print(f"  ❌ FAILED!")
            return False
        print()
    
    print("🎉 All parsing tests passed!")
    return True

if __name__ == "__main__":
    test_crypto_parsing()
