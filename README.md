# Telegram AI Assistant Bot

A powerful Telegram userbot (self-bot) that integrates multiple AI services including Google's Gemini, Groq, Cloudflare AI, and more. Features include text generation, image generation/editing, document processing, translation, and text-to-speech capabilities.

## Features

### AI Chat & Text Generation
- Multiple AI providers support (Gemini, Groq, Cloudflare, OpenRouter)
- Context-aware chat with history
- Custom prompt modes for different use cases
- Model selection and configuration

### Document Processing
- Upload and process documents using Gemini File API
- Document summarization
- Entity extraction
- Question answering about documents
- HTML transcription
- Supports multiple file formats (PDF, Python, JavaScript, Text, etc.)

### Translation
- Multiple translation providers:
  - Gemini (AI-powered, context-aware)
  - Google Translate (fast, basic)
  - Cloudflare (M2M100 model)
  - OpenRouter (various LLMs)
- 15+ supported languages
- Custom default settings per user

### Image Generation & Editing
- Multiple models support:
  - Gemini
  - Google's Imagen
  - Cloudflare's Dreamshaper & Flux
- Image editing capabilities
- Various aspect ratios
- Multiple image generation

### Text-to-Speech
- MeloTTS integration via Cloudflare
- Multiple language support
- High-quality voice synthesis

### Cryptocurrency Analysis
- Real-time cryptocurrency data from CoinMarketCap
- AI-powered technical analysis
- Professional trading insights
- Support for all major cryptocurrencies
- Multiple AI providers for analysis

## Prerequisites

- Python 3.8+
- MongoDB database
- Telegram API credentials (api_id and api_hash)
- API keys for AI services:
  - Google (Gemini)
  - Groq
  - Cloudflare
  - OpenRouter (optional)
  - CoinMarketCap (for cryptocurrency analysis)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/telegram-ai-assistant.git
cd telegram-ai-assistant
```

2. Create and activate virtual environment:
```bash
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create environment configuration:
```bash
cp .env.example .env
```

5. Edit `.env` with your credentials:
```bash
# Telegram Configuration
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash
TELEGRAM_SESSION=my_account

# AI Provider API Keys
GOOGLE_API_KEY=your_google_gemini_api_key
GROQ_API_KEY=your_groq_api_key
OPENROUTER_API_KEY=your_openrouter_api_key
MINITOOL_API_KEY=your_minitool_api_key

# Cryptocurrency API Keys
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key

# Cloudflare AI Configuration
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_API_KEY=your_cloudflare_api_key

# Proxy Configuration (optional)
PROXY_ENABLED=false
PROXY_SCHEME=socks5
PROXY_HOSTNAME=localhost
PROXY_PORT=10808
```

**Migration from config.yaml:** If you have an existing `config.yaml`, run the migration script:
```bash
python migrate_config.py
```

## Usage

1. Start the bot:
```bash
python main.py
```

2. Available commands:
- `.help`: Show main help menu
- `.help translate`: Translation help
- `.help genimg`: Image generation help
- `.help profile`: Profile management help

### Basic Commands:
- `.ask <question>`: Ask AI a question
- `.chat <message>`: Chat with AI
- `.translate <text>`: Translate text
- `.genimg <prompt>`: Generate images
- `.docprocess <action>`: Process documents
- `.tts <text>`: Text to speech
- `.crypto <symbol> [provider] [language]`: Analyze cryptocurrency (e.g., `.crypto BTC -g -fa`)
- `.btcd [provider] [language]`: Analyze Bitcoin dominance (e.g., `.btcd -g -fa`)
- `.cryptokey <api_key>`: Set CoinMarketCap API key

## Project Structure

```
├── main.py              # Main entry point
├── .env                 # Environment configuration
├── .env.example         # Environment template
├── migrate_config.py    # Migration script from config.yaml
├── requirements.txt     # Python dependencies
├── plugins/            # Command plugins
│   ├── help_plug.py    # Help commands
│   ├── tools_plug.py   # Utility tools
│   ├── translate_plug.py # Translation
│   ├── crypto_plug.py  # Cryptocurrency analysis
│   └── ...
├── helpers/            # Helper modules
│   ├── __init__.py    # Shared utilities
│   ├── config.py      # Config management
│   └── ...
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Thanks to all AI providers for their APIs
- Pyrogram for the excellent Telegram client library