# Text-to-Speech (TTS) Usage Guide

## Overview
The selfbot now supports three high-quality TTS providers:
- **Gemini** (Premium quality, 30 voices, 24 languages)
- **Groq** (High quality, 23 voices, English/Arabic)
- **Cloudflare** (Fast, efficient, multilingual)

## Basic Usage

### Simple TTS (Default: Cloudflare)
```
.tts Hello, this is a test message!        # No caption (clean voice)
.tts -v Hello, this is a test message!     # With caption
```

### Using Gemini TTS (Recommended for best quality)
```
.tts -gemini Hello, this is premium quality speech!     # No caption
.tts -v -gemini Hello, this is premium quality speech!  # With caption
```

### Using Groq TTS
```
.tts -groq Hello, this is high quality speech!          # No caption
.tts -v -groq Hello, this is high quality speech!       # With caption
```

### Caption Control
- **Default behavior:** Voice messages sent without captions (clean)
- **With `-v` flag:** Shows caption with text and style information
- **Format:** `🗣 Text...` (normal) or `🎭 Style • Text...` (with style)

## Voice Management

### Setting Default Voices
Set your preferred voice for each provider:
```
.ttsvoice gemini Despina          # Set Gemini default voice
.ttsvoice groq Celeste-PlayAI     # Set Groq default voice
.ttsreset                         # Reset to default voices
```

### Listing Available Voices
```
.ttsvoices                        # Show all providers and current defaults
.ttsvoices gemini                 # List all Gemini voices
.ttsvoices groq                   # List all Groq voices
```

### Voice Selection Methods

#### Method 1: Use Default Voice (Recommended)
```
.tts -gemini Hello world!         # Uses your default Gemini voice
.tts -groq Hello world!           # Uses your default Groq voice
```

#### Method 2: Override Voice for One Message
```
.tts -gemini voice=Puck I sound upbeat and energetic!
.tts -gemini voice=Kore I have a firm and confident voice.
.tts -gemini voice=Charon I provide informative narration.
.tts -gemini voice=Enceladus I speak with a breathy tone.
.tts -gemini voice=Sulafat I have a warm and friendly voice.
```

**Popular Gemini Voices:**
- `Kore` - Firm and confident
- `Puck` - Upbeat and energetic  
- `Charon` - Informative and clear
- `Zephyr` - Bright and cheerful
- `Fenrir` - Excitable and dynamic
- `Enceladus` - Breathy and soft
- `Achernar` - Soft and gentle
- `Sulafat` - Warm and friendly

### Groq Voices
```
.tts -groq voice=Fritz-PlayAI This is Fritz speaking!
.tts -groq voice=Celeste-PlayAI I'm Celeste with a lovely voice.
.tts -groq voice=Thunder-PlayAI I have a powerful voice!
```

## Language Support

### Multilingual Examples
```
.tts -gemini fr Bonjour, comment allez-vous?
.tts -gemini es Hola, ¿cómo estás?
.tts -gemini de Hallo, wie geht es dir?
.tts -groq ar مرحبا بالعالم
```

**Supported Languages:**
English, French, Spanish, German, Italian, Portuguese, Polish, Turkish, Russian, Dutch, Czech, Arabic, Chinese, Japanese, Korean, and more.

## Reply to Messages

Smart reply functionality with colon-based control:

### Reply Modes

1. **Read replied message with style:**
   ```
   .tts -gemini read cheerfully:
   .tts -gemini voice=Puck speak dramatically:
   ```

2. **Reply with custom text:**
   ```
   .tts -gemini say softly: Good night everyone
   .tts -gemini voice=Kore respond: Thank you for your message
   ```

3. **Simple style prompt (reads replied message):**
   ```
   .tts -gemini read this cheerfully
   .tts -gemini voice=Puck say this dramatically
   ```

## Style Control (Gemini Only)

You can control the speech style, tone, and delivery in two ways:

### Method 1: Inline Style (Works Everywhere)
Use a colon (`:`) to separate the style instruction from the text:
```
.tts -gemini Say cheerfully: Hello world!
.tts -gemini Read dramatically: Welcome to our presentation
.tts -gemini Speak softly: Good night everyone
.tts -gemini voice=Puck Say excitedly: This is amazing news!
```

### Method 2: Reply Style Prompts
When replying to messages, add style instructions:
```
.tts -gemini read this cheerfully
.tts -gemini say in a spooky whisper
.tts -gemini speak in a professional tone
.tts -gemini voice=Kore read this dramatically
.tts -gemini voice=Enceladus say this softly and gently
```

### Popular Style Prompts
- `Say cheerfully` / `read this cheerfully` - Upbeat and happy delivery
- `Speak dramatically` / `say in a spooky whisper` - Dramatic, mysterious tone
- `Read professionally` / `speak professionally` - Business-like, formal tone
- `Say excitedly` / `speak with excitement` - Energetic, enthusiastic delivery
- `Speak softly` / `say this softly` - Gentle, quiet tone
- `Read sadly` / `read this sadly` - Melancholic, somber tone
- `Say mysteriously` - Intriguing, secretive tone

## Advanced Examples

### Professional Narration
```
.tts -gemini voice=Charon Welcome to our presentation on artificial intelligence.
```

### Casual Conversation
```
.tts -gemini voice=Puck Hey everyone! Hope you're having a great day!
```

### Soft Announcement
```
.tts -gemini voice=Achernar Please remember to turn off your devices.
```

### Energetic Message
```
.tts -gemini voice=Fenrir Let's get this party started!
```

## Tips for Best Results

1. **Use Gemini for premium quality** - It offers the best voice quality and most natural speech
2. **Choose appropriate voices** - Match the voice personality to your message tone
3. **Keep messages concise** - Shorter messages work better for voice messages
4. **Try different voices** - Experiment to find your favorites
5. **Use language codes** - Specify language for non-English text

## Provider Comparison

| Provider   | Quality | Voices | Languages | Speed | Best For |
|------------|---------|--------|-----------|-------|----------|
| Gemini     | Premium | 30     | 24        | Fast  | All purposes |
| Groq       | High    | 23     | 2         | Fast  | English/Arabic |
| Cloudflare | Good    | 1      | 15+       | Fastest | Quick messages |

## Troubleshooting

If TTS doesn't work:
1. Check your API keys are configured
2. Ensure you have internet connection
3. Try a different provider
4. Check the help: `.help tts`

## Getting Help

- `.help tts` - Detailed TTS help
- `.help` - General bot help

Enjoy high-quality text-to-speech with your selfbot! 🎵
